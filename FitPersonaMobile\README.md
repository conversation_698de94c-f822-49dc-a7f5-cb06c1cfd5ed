# 💪 FitPersona Mobile

Kişisel fitness asistanınız - BMI hesaplama ve kişiselleştirilmiş egzersiz/beslenme önerileri sunan mobil uygulama.

## 🚀 Özellikler

- **BMI Hesaplama**: Boy, kilo, yaş ve cinsiyete göre BMI hesaplama
- **Kişiselleştirilmiş Öneriler**: BMI sonucuna göre egzersiz ve beslenme tavsiyeleri
- **Modern UI/UX**: Koyu tema ve kullanıcı dostu arayüz
- **API Entegrasyonu**: FitPersona AI agent ile bağlantı
- **Cross-Platform**: iOS, Android ve Web desteği

## 📱 Ekran Görüntüleri

Uygulama şu özellikleri içerir:
- Ana form ekranı (boy, kilo, yaş, cinsiyet girişi)
- BMI sonuç kartı
- Egzersiz önerileri
- Beslenme tavsiyeleri
- Yeniden hesaplama butonu

## 🛠️ Kurulum

### Gereksinimler
- Node.js (v16 veya üzeri)
- Expo CLI
- Expo Go uygulaması (mobil test için)

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repo-url>
cd FitPersonaMobile
```

2. **Bağımlılıkları yükleyin**
```bash
npm install
```

3. **Uygulamayı başlatın**
```bash
npx expo start
```

4. **Test edin**
- Web: `w` tuşuna basın
- Android: `a` tuşuna basın (Android Studio gerekli)
- iOS: `i` tuşuna basın (Xcode gerekli)
- Mobil: QR kodu Expo Go ile tarayın

## 🔧 API Bağlantısı

Uygulama, FitPersona AI agent'ına bağlanır:
- **Endpoint**: `http://localhost:4111/api/agents/fitPersona/generate`
- **Method**: POST
- **Payload**: Kullanıcı bilgileri (boy, kilo, yaş, cinsiyet)

API bağlantısı başarısız olursa, varsayılan öneriler gösterilir.

## 📦 Build ve Deploy

### Development Build
```bash
npx expo build:android
npx expo build:ios
```

### Production Build
```bash
npx expo build:android --release-channel production
npx expo build:ios --release-channel production
```

### Web Deploy
```bash
npx expo export:web
```

## 🎨 Tema ve Renkler

- **Ana Tema**: Koyu tema (#1a1a2e)
- **Kart Arka Planı**: #16213e
- **Metin Rengi**: #fff (beyaz)
- **İkincil Metin**: #a0a0a0
- **Vurgu Rengi**: #e74c3c (kırmızı)
- **Başarı Rengi**: #2ecc71 (yeşil)

## 📱 Platform Desteği

- ✅ **iOS**: iPhone ve iPad
- ✅ **Android**: Tüm Android cihazlar
- ✅ **Web**: Modern tarayıcılar

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📞 İletişim

Sorularınız için: [<EMAIL>]

---

**FitPersona** - Sağlıklı yaşamın dijital asistanı 💪
