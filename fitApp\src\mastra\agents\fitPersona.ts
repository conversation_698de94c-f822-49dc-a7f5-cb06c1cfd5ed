import { Agent } from '@mastra/core';
import { z } from 'zod';

// BMI hesaplama fonksiyonu
function calculateBMI(height_cm: number, weight_kg: number) {
  const height_m = height_cm / 100;
  const bmi = weight_kg / (height_m * height_m);
  
  let category = '';
  let recommendation = '';
  
  if (bmi < 18.5) {
    category = 'Zayıf';
    recommendation = 'Kilo almanız önerilir. Sağlıklı beslenme ve güç antrenmanları yapın.';
  } else if (bmi >= 18.5 && bmi < 25) {
    category = 'Normal';
    recommendation = 'Mükemmel! Mevcut kilonuzu koruyun.';
  } else if (bmi >= 25 && bmi < 30) {
    category = 'Fazla Kilolu';
    recommendation = 'Kilo vermeniz önerilir. Kalori açığı oluşturun ve düzenli egzersiz yapın.';
  } else {
    category = 'Obez';
    recommendation = 'Acil kilo vermeniz gerekiyor. Doktor kontrolünde diyet ve egzersiz programı başlatın.';
  }
  
  return {
    bmi: Math.round(bmi * 10) / 10,
    category,
    recommendation
  };
}

// BMR hesaplama fonksiyonu (Harris-Benedict)
function calculateBMR(weight_kg: number, height_cm: number, age: number, gender: string) {
  if (gender.toLowerCase() === 'male') {
    return 88.362 + (13.397 * weight_kg) + (4.799 * height_cm) - (5.677 * age);
  } else {
    return 447.593 + (9.247 * weight_kg) + (3.098 * height_cm) - (4.330 * age);
  }
}

// Günlük kalori ihtiyacı hesaplama
function calculateDailyCalories(bmr: number, activity_level: string) {
  const multipliers = {
    'sedentary': 1.2,      // Hareketsiz
    'light': 1.375,        // Hafif aktif
    'moderate': 1.55,      // Orta aktif
    'active': 1.725,       // Çok aktif
    'very_active': 1.9     // Aşırı aktif
  };
  
  return Math.round(bmr * (multipliers[activity_level as keyof typeof multipliers] || 1.55));
}

// Beslenme planı oluşturma
function createNutritionPlan(bmi: number, dailyCalories: number, gender: string) {
  let goal = '';
  let calorieAdjustment = 0;
  
  if (bmi < 18.5) {
    goal = 'kilo_alma';
    calorieAdjustment = 300; // Fazla kalori
  } else if (bmi >= 25) {
    goal = 'kilo_verme';
    calorieAdjustment = -500; // Kalori açığı
  } else {
    goal = 'kilo_koruma';
    calorieAdjustment = 0;
  }
  
  const targetCalories = dailyCalories + calorieAdjustment;
  
  // Makro besin dağılımı
  const protein = Math.round(targetCalories * 0.25 / 4); // %25 protein (4 kcal/g)
  const carbs = Math.round(targetCalories * 0.45 / 4);   // %45 karbonhidrat (4 kcal/g)
  const fat = Math.round(targetCalories * 0.30 / 9);     // %30 yağ (9 kcal/g)
  
  return {
    goal,
    targetCalories,
    macros: {
      protein: `${protein}g`,
      carbs: `${carbs}g`,
      fat: `${fat}g`
    },
    mealPlan: {
      breakfast: "Yumurta, tam tahıl ekmek, avokado",
      lunch: "Izgara tavuk, quinoa, sebze salatası",
      dinner: "Balık, buharda sebze, bulgur pilavı",
      snacks: "Kuruyemiş, meyve, yoğurt"
    }
  };
}

// Egzersiz planı oluşturma
function createExercisePlan(bmi: number, age: number, gender: string) {
  let intensity = '';
  let frequency = '';
  let duration = '';
  let exercises = [];
  
  if (bmi < 18.5) {
    intensity = 'orta';
    frequency = '3-4 gün/hafta';
    duration = '45-60 dakika';
    exercises = [
      'Ağırlık antrenmanı',
      'Compound hareketler (squat, deadlift)',
      'Protein açısından zengin beslenme'
    ];
  } else if (bmi >= 25) {
    intensity = 'yüksek';
    frequency = '5-6 gün/hafta';
    duration = '60-90 dakika';
    exercises = [
      'HIIT antrenmanı',
      'Kardiyovasküler egzersizler',
      'Güç antrenmanı',
      'Yürüyüş/koşu'
    ];
  } else {
    intensity = 'orta';
    frequency = '4-5 gün/hafta';
    duration = '45-60 dakika';
    exercises = [
      'Karma antrenman',
      'Kardiyovasküler + güç',
      'Esneklik çalışmaları',
      'Spor aktiviteleri'
    ];
  }
  
  return {
    intensity,
    frequency,
    duration,
    exercises,
    weeklyPlan: {
      monday: 'Üst vücut güç antrenmanı',
      tuesday: 'Kardiyovasküler egzersiz',
      wednesday: 'Alt vücut güç antrenmanı',
      thursday: 'HIIT antrenmanı',
      friday: 'Tam vücut antrenmanı',
      saturday: 'Aktif dinlenme (yürüyüş)',
      sunday: 'Dinlenme günü'
    }
  };
}

export const fitPersonaAgent = new Agent({
  name: 'FitPersona',
  instructions: `Sen FitPersona'sın - kişiye özel fitness ve beslenme asistanı. 
  BMI hesaplama, beslenme planı ve egzersiz önerileri sunuyorsun.
  Türkçe yanıt ver ve kullanıcıya samimi bir şekilde yaklaş.`,
  
  model: {
    provider: 'google',
    name: 'gemini-1.5-flash',
  },
  
  tools: {
    calculateBMI: {
      description: 'Kullanıcının boy ve kilosuna göre BMI hesaplar',
      parameters: z.object({
        height_cm: z.number().describe('Boy (cm cinsinden)'),
        weight_kg: z.number().describe('Kilo (kg cinsinden)')
      }),
      execute: async ({ height_cm, weight_kg }) => {
        return calculateBMI(height_cm, weight_kg);
      }
    },
    
    createNutritionPlan: {
      description: 'Kişiye özel beslenme planı oluşturur',
      parameters: z.object({
        weight_kg: z.number().describe('Kilo (kg)'),
        height_cm: z.number().describe('Boy (cm)'),
        age: z.number().describe('Yaş'),
        gender: z.string().describe('Cinsiyet (male/female)'),
        activity_level: z.string().describe('Aktivite seviyesi (sedentary/light/moderate/active/very_active)')
      }),
      execute: async ({ weight_kg, height_cm, age, gender, activity_level }) => {
        const bmi = calculateBMI(height_cm, weight_kg).bmi;
        const bmr = calculateBMR(weight_kg, height_cm, age, gender);
        const dailyCalories = calculateDailyCalories(bmr, activity_level);
        
        return {
          bmi,
          bmr: Math.round(bmr),
          dailyCalories,
          nutritionPlan: createNutritionPlan(bmi, dailyCalories, gender)
        };
      }
    },
    
    createExercisePlan: {
      description: 'Kişiye özel egzersiz planı oluşturur',
      parameters: z.object({
        weight_kg: z.number().describe('Kilo (kg)'),
        height_cm: z.number().describe('Boy (cm)'),
        age: z.number().describe('Yaş'),
        gender: z.string().describe('Cinsiyet (male/female)')
      }),
      execute: async ({ weight_kg, height_cm, age, gender }) => {
        const bmi = calculateBMI(height_cm, weight_kg).bmi;
        return createExercisePlan(bmi, age, gender);
      }
    },
    
    getCompleteFitnessPlan: {
      description: 'Komple fitness planı (BMI + beslenme + egzersiz) oluşturur',
      parameters: z.object({
        weight_kg: z.number().describe('Kilo (kg)'),
        height_cm: z.number().describe('Boy (cm)'),
        age: z.number().describe('Yaş'),
        gender: z.string().describe('Cinsiyet (male/female)'),
        activity_level: z.string().describe('Aktivite seviyesi')
      }),
      execute: async ({ weight_kg, height_cm, age, gender, activity_level }) => {
        const bmiResult = calculateBMI(height_cm, weight_kg);
        const bmr = calculateBMR(weight_kg, height_cm, age, gender);
        const dailyCalories = calculateDailyCalories(bmr, activity_level);
        const nutritionPlan = createNutritionPlan(bmiResult.bmi, dailyCalories, gender);
        const exercisePlan = createExercisePlan(bmiResult.bmi, age, gender);
        
        return {
          userProfile: { weight_kg, height_cm, age, gender, activity_level },
          bmiAnalysis: bmiResult,
          nutritionPlan: { bmr: Math.round(bmr), dailyCalories, ...nutritionPlan },
          exercisePlan,
          summary: `Merhaba! BMI'niz ${bmiResult.bmi} (${bmiResult.category}). ${bmiResult.recommendation}`
        };
      }
    }
  }
});
