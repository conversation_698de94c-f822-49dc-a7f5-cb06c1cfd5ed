{"version": 3, "file": "sdk-info.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/sdk-info.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EACL,uBAAuB,EACvB,2BAA2B,EAC3B,kCAAkC,EAClC,0BAA0B,GAC3B,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,yBAAyB,EAAE,MAAM,eAAe,CAAC;AAE1D,0CAA0C;AAC1C,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,CAAC,uBAAuB,CAAC,EAAE,eAAe;IAC1C,CAAC,yBAAyB,CAAC,EAAE,SAAS;IACtC,CAAC,2BAA2B,CAAC,EAAE,kCAAkC;IACjE,CAAC,0BAA0B,CAAC,EAAE,OAAO;CACtC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../../version';\nimport {\n  ATTR_TELEMETRY_SDK_NAME,\n  ATTR_TELEMETRY_SDK_LANGUAGE,\n  TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS,\n  ATTR_TELEMETRY_SDK_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { ATTR_PROCESS_RUNTIME_NAME } from '../../semconv';\n\n/** Constants describing the SDK in use */\nexport const SDK_INFO = {\n  [ATTR_TELEMETRY_SDK_NAME]: 'opentelemetry',\n  [ATTR_PROCESS_RUNTIME_NAME]: 'browser',\n  [ATTR_TELEMETRY_SDK_LANGUAGE]: TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS,\n  [ATTR_TELEMETRY_SDK_VERSION]: VERSION,\n};\n"]}