{"version": 3, "file": "otlp-http-configuration.js", "sourceRoot": "", "sources": ["../../../src/configuration/otlp-http-configuration.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,8BAA8B,EAC9B,wCAAwC,GAEzC,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,2BAA2B,EAAE,MAAM,SAAS,CAAC;AAYtD,SAAS,YAAY,CACnB,mBAAsE,EACtE,eAAkE,EAClE,cAA4C;IAE5C,MAAM,eAAe,GAAG;QACtB,GAAG,cAAc,EAAE;KACpB,CAAC;IACF,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,OAAO,GAAG,EAAE;QACV,0BAA0B;QAC1B,IAAI,eAAe,IAAI,IAAI,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;SAC3C;QAED,mCAAmC;QACnC,IAAI,mBAAmB,IAAI,IAAI,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;SAC/C;QAED,0BAA0B;QAC1B,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAAC,GAAuB;IACtD,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,SAAS,CAAC;KAClB;IACD,IAAI;QACF,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,GAAG,CAAC;KACZ;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,IAAI,KAAK,CACb,6DAA6D,GAAG,GAAG,CACpE,CAAC;KACH;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,sCAAsC,CACpD,yBAAyD,EACzD,qBAAqD,EACrD,oBAA2C;IAE3C,OAAO;QACL,GAAG,wCAAwC,CACzC,yBAAyB,EACzB,qBAAqB,EACrB,oBAAoB,CACrB;QACD,OAAO,EAAE,YAAY,CACnB,2BAA2B,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAC9D,qBAAqB,CAAC,OAAO,EAC7B,oBAAoB,CAAC,OAAO,CAC7B;QACD,GAAG,EACD,uBAAuB,CAAC,yBAAyB,CAAC,GAAG,CAAC;YACtD,qBAAqB,CAAC,GAAG;YACzB,oBAAoB,CAAC,GAAG;QAC1B,YAAY,EACV,yBAAyB,CAAC,YAAY;YACtC,qBAAqB,CAAC,YAAY;YAClC,oBAAoB,CAAC,YAAY;KACpC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,eAAuC,EACvC,kBAA0B;IAE1B,OAAO;QACL,GAAG,8BAA8B,EAAE;QACnC,OAAO,EAAE,GAAG,EAAE,CAAC,eAAe;QAC9B,GAAG,EAAE,wBAAwB,GAAG,kBAAkB;QAClD,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;KAClC,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  getSharedConfigurationDefaults,\n  mergeOtlpSharedConfigurationWithDefaults,\n  OtlpSharedConfiguration,\n} from './shared-configuration';\nimport { validateAndNormalizeHeaders } from '../util';\n\n// NOTE: do not change these imports to be actual imports, otherwise they WILL break `@opentelemetry/instrumentation-http`\nimport type * as http from 'http';\nimport type * as https from 'https';\n\nexport interface OtlpHttpConfiguration extends OtlpSharedConfiguration {\n  url: string;\n  headers: () => Record<string, string>;\n  agentOptions: http.AgentOptions | https.AgentOptions;\n}\n\nfunction mergeHeaders(\n  userProvidedHeaders: (() => Record<string, string>) | undefined | null,\n  fallbackHeaders: (() => Record<string, string>) | undefined | null,\n  defaultHeaders: () => Record<string, string>\n): () => Record<string, string> {\n  const requiredHeaders = {\n    ...defaultHeaders(),\n  };\n  const headers = {};\n\n  return () => {\n    // add fallback ones first\n    if (fallbackHeaders != null) {\n      Object.assign(headers, fallbackHeaders());\n    }\n\n    // override with user-provided ones\n    if (userProvidedHeaders != null) {\n      Object.assign(headers, userProvidedHeaders());\n    }\n\n    // override required ones.\n    return Object.assign(headers, requiredHeaders);\n  };\n}\n\nfunction validateUserProvidedUrl(url: string | undefined): string | undefined {\n  if (url == null) {\n    return undefined;\n  }\n  try {\n    new URL(url);\n    return url;\n  } catch (e) {\n    throw new Error(\n      `Configuration: Could not parse user-provided export URL: '${url}'`\n    );\n  }\n}\n\n/**\n * @param userProvidedConfiguration  Configuration options provided by the user in code.\n * @param fallbackConfiguration Fallback to use when the {@link userProvidedConfiguration} does not specify an option.\n * @param defaultConfiguration The defaults as defined by the exporter specification\n */\nexport function mergeOtlpHttpConfigurationWithDefaults(\n  userProvidedConfiguration: Partial<OtlpHttpConfiguration>,\n  fallbackConfiguration: Partial<OtlpHttpConfiguration>,\n  defaultConfiguration: OtlpHttpConfiguration\n): OtlpHttpConfiguration {\n  return {\n    ...mergeOtlpSharedConfigurationWithDefaults(\n      userProvidedConfiguration,\n      fallbackConfiguration,\n      defaultConfiguration\n    ),\n    headers: mergeHeaders(\n      validateAndNormalizeHeaders(userProvidedConfiguration.headers),\n      fallbackConfiguration.headers,\n      defaultConfiguration.headers\n    ),\n    url:\n      validateUserProvidedUrl(userProvidedConfiguration.url) ??\n      fallbackConfiguration.url ??\n      defaultConfiguration.url,\n    agentOptions:\n      userProvidedConfiguration.agentOptions ??\n      fallbackConfiguration.agentOptions ??\n      defaultConfiguration.agentOptions,\n  };\n}\n\nexport function getHttpConfigurationDefaults(\n  requiredHeaders: Record<string, string>,\n  signalResourcePath: string\n): OtlpHttpConfiguration {\n  return {\n    ...getSharedConfigurationDefaults(),\n    headers: () => requiredHeaders,\n    url: 'http://localhost:4318/' + signalResourcePath,\n    agentOptions: { keepAlive: true },\n  };\n}\n"]}