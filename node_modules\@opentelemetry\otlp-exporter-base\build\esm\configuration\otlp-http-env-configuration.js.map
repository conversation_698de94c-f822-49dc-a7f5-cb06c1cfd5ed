{"version": 3, "file": "otlp-http-env-configuration.js", "sourceRoot": "", "sources": ["../../../src/configuration/otlp-http-env-configuration.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAE,uBAAuB,EAAE,MAAM,qBAAqB,CAAC;AAC9D,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,qCAAqC,EAAE,MAAM,4BAA4B,CAAC;AAEnF,OAAO,EAAE,2BAA2B,EAAE,MAAM,wBAAwB,CAAC;AAErE,SAAS,uBAAuB,CAC9B,gBAAwB;IAExB,MAAM,wBAAwB,GAC5B,OAAO,CAAC,GAAG,CAAC,sBAAsB,gBAAgB,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC;IACxE,MAAM,2BAA2B,GAC/B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,IAAI,EAAE,CAAC;IAEpD,MAAM,qBAAqB,GAAG,uBAAuB,CACnD,wBAAwB,CACzB,CAAC;IACF,MAAM,wBAAwB,GAAG,uBAAuB,CACtD,2BAA2B,CAC5B,CAAC;IAEF,IACE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,MAAM,KAAK,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM,KAAK,CAAC,EAClD;QACA,OAAO,SAAS,CAAC;KAClB;IAED,gGAAgG;IAChG,yBAAyB;IACzB,OAAO,MAAM,CAAC,MAAM,CAClB,EAAE,EACF,uBAAuB,CAAC,2BAA2B,CAAC,EACpD,uBAAuB,CAAC,wBAAwB,CAAC,CAClD,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B,CAAC,GAAW;IAC9C,IAAI;QACF,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,8DAA8D;QAC9D,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC7B;IAAC,MAAM;QACN,IAAI,CAAC,IAAI,CACP,oEAAoE,GAAG,8BAA8B,CACtG,CAAC;QACF,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,uBAAuB,CAC9B,GAAW,EACX,IAAY;IAEZ,IAAI;QACF,oDAAoD;QACpD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;KACd;IAAC,MAAM;QACN,IAAI,CAAC,IAAI,CACP,oEAAoE,GAAG,8BAA8B,CACtG,CAAC;QACF,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KACjB;IACD,GAAG,IAAI,IAAI,CAAC;IAEZ,IAAI;QACF,oDAAoD;QACpD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;KACd;IAAC,MAAM;QACN,IAAI,CAAC,IAAI,CACP,8CAA8C,IAAI,uDAAuD,GAAG,GAAG,CAChH,CAAC;QACF,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,wBAAwB,CAC/B,kBAA0B;IAE1B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,IAAI,EAAE,CAAC;IAC/D,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,EAAE,EAAE;QACnC,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,uBAAuB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;AAC7D,CAAC;AAED,SAAS,qBAAqB,CAAC,gBAAwB;IACrD,MAAM,MAAM,GACV,OAAO,CAAC,GAAG,CAAC,sBAAsB,gBAAgB,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC;IACzE,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,EAAE,EAAE;QACnC,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,2BAA2B,CAAC,MAAM,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mCAAmC,CACjD,gBAAwB,EACxB,kBAA0B;IAE1B,OAAO;QACL,GAAG,qCAAqC,CAAC,gBAAgB,CAAC;QAC1D,GAAG,EACD,qBAAqB,CAAC,gBAAgB,CAAC;YACvC,wBAAwB,CAAC,kBAAkB,CAAC;QAC9C,OAAO,EAAE,2BAA2B,CAClC,uBAAuB,CAAC,gBAAgB,CAAC,CAC1C;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { parseKeyPairsIntoRecord } from '@opentelemetry/core';\nimport { diag } from '@opentelemetry/api';\nimport { getSharedConfigurationFromEnvironment } from './shared-env-configuration';\nimport { OtlpHttpConfiguration } from './otlp-http-configuration';\nimport { wrapStaticHeadersInFunction } from './shared-configuration';\n\nfunction getStaticHeadersFromEnv(\n  signalIdentifier: string\n): Record<string, string> | undefined {\n  const signalSpecificRawHeaders =\n    process.env[`OTEL_EXPORTER_OTLP_${signalIdentifier}_HEADERS`]?.trim();\n  const nonSignalSpecificRawHeaders =\n    process.env['OTEL_EXPORTER_OTLP_HEADERS']?.trim();\n\n  const signalSpecificHeaders = parseKeyPairsIntoRecord(\n    signalSpecificRawHeaders\n  );\n  const nonSignalSpecificHeaders = parseKeyPairsIntoRecord(\n    nonSignalSpecificRawHeaders\n  );\n\n  if (\n    Object.keys(signalSpecificHeaders).length === 0 &&\n    Object.keys(nonSignalSpecificHeaders).length === 0\n  ) {\n    return undefined;\n  }\n\n  // headers are combined instead of overwritten, with the specific headers taking precedence over\n  // the non-specific ones.\n  return Object.assign(\n    {},\n    parseKeyPairsIntoRecord(nonSignalSpecificRawHeaders),\n    parseKeyPairsIntoRecord(signalSpecificRawHeaders)\n  );\n}\n\nfunction appendRootPathToUrlIfNeeded(url: string): string | undefined {\n  try {\n    const parsedUrl = new URL(url);\n    // This will automatically append '/' if there's no root path.\n    return parsedUrl.toString();\n  } catch {\n    diag.warn(\n      `Configuration: Could not parse environment-provided export URL: '${url}', falling back to undefined`\n    );\n    return undefined;\n  }\n}\n\nfunction appendResourcePathToUrl(\n  url: string,\n  path: string\n): string | undefined {\n  try {\n    // just try to parse, if it fails we catch and warn.\n    new URL(url);\n  } catch {\n    diag.warn(\n      `Configuration: Could not parse environment-provided export URL: '${url}', falling back to undefined`\n    );\n    return undefined;\n  }\n\n  if (!url.endsWith('/')) {\n    url = url + '/';\n  }\n  url += path;\n\n  try {\n    // just try to parse, if it fails we catch and warn.\n    new URL(url);\n  } catch {\n    diag.warn(\n      `Configuration: Provided URL appended with '${path}' is not a valid URL, using 'undefined' instead of '${url}'`\n    );\n    return undefined;\n  }\n\n  return url;\n}\n\nfunction getNonSpecificUrlFromEnv(\n  signalResourcePath: string\n): string | undefined {\n  const envUrl = process.env.OTEL_EXPORTER_OTLP_ENDPOINT?.trim();\n  if (envUrl == null || envUrl === '') {\n    return undefined;\n  }\n  return appendResourcePathToUrl(envUrl, signalResourcePath);\n}\n\nfunction getSpecificUrlFromEnv(signalIdentifier: string): string | undefined {\n  const envUrl =\n    process.env[`OTEL_EXPORTER_OTLP_${signalIdentifier}_ENDPOINT`]?.trim();\n  if (envUrl == null || envUrl === '') {\n    return undefined;\n  }\n  return appendRootPathToUrlIfNeeded(envUrl);\n}\n\n/**\n * Reads and returns configuration from the environment\n *\n * @param signalIdentifier all caps part in environment variables that identifies the signal (e.g.: METRICS, TRACES, LOGS)\n * @param signalResourcePath signal resource path to append if necessary (e.g.: v1/metrics, v1/traces, v1/logs)\n */\nexport function getHttpConfigurationFromEnvironment(\n  signalIdentifier: string,\n  signalResourcePath: string\n): Partial<OtlpHttpConfiguration> {\n  return {\n    ...getSharedConfigurationFromEnvironment(signalIdentifier),\n    url:\n      getSpecificUrlFromEnv(signalIdentifier) ??\n      getNonSpecificUrlFromEnv(signalResourcePath),\n    headers: wrapStaticHeadersInFunction(\n      getStaticHeadersFromEnv(signalIdentifier)\n    ),\n  };\n}\n"]}