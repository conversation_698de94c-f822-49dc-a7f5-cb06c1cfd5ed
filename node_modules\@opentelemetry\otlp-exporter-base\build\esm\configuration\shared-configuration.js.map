{"version": 3, "file": "shared-configuration.js", "sourceRoot": "", "sources": ["../../../src/configuration/shared-configuration.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAgBH,MAAM,UAAU,qBAAqB,CAAC,aAAqB;IACzD,IAAI,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,aAAa,GAAG,CAAC,EAAE;QACvD,OAAO,aAAa,CAAC;KACtB;IACD,MAAM,IAAI,KAAK,CACb,qFAAqF,aAAa,IAAI,CACvG,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,2BAA2B,CACzC,OAA2C;IAE3C,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AACvB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,wCAAwC,CACtD,yBAA2D,EAC3D,qBAAuD,EACvD,oBAA6C;IAE7C,OAAO;QACL,aAAa,EAAE,qBAAqB,CAClC,yBAAyB,CAAC,aAAa;YACrC,qBAAqB,CAAC,aAAa;YACnC,oBAAoB,CAAC,aAAa,CACrC;QACD,gBAAgB,EACd,yBAAyB,CAAC,gBAAgB;YAC1C,qBAAqB,CAAC,gBAAgB;YACtC,oBAAoB,CAAC,gBAAgB;QACvC,WAAW,EACT,yBAAyB,CAAC,WAAW;YACrC,qBAAqB,CAAC,WAAW;YACjC,oBAAoB,CAAC,WAAW;KACnC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,8BAA8B;IAC5C,OAAO;QACL,aAAa,EAAE,KAAK;QACpB,gBAAgB,EAAE,EAAE;QACpB,WAAW,EAAE,MAAM;KACpB,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Configuration shared across all OTLP exporters\n *\n * Implementation note: anything added here MUST be\n * - platform-agnostic\n * - signal-agnostic\n * - transport-agnostic\n */\nexport interface OtlpSharedConfiguration {\n  timeoutMillis: number;\n  concurrencyLimit: number;\n  compression: 'gzip' | 'none';\n}\n\nexport function validateTimeoutMillis(timeoutMillis: number) {\n  if (Number.isFinite(timeoutMillis) && timeoutMillis > 0) {\n    return timeoutMillis;\n  }\n  throw new Error(\n    `Configuration: timeoutMillis is invalid, expected number greater than 0 (actual: '${timeoutMillis}')`\n  );\n}\n\nexport function wrapStaticHeadersInFunction(\n  headers: Record<string, string> | undefined\n): (() => Record<string, string>) | undefined {\n  if (headers == null) {\n    return undefined;\n  }\n\n  return () => headers;\n}\n\n/**\n * @param userProvidedConfiguration  Configuration options provided by the user in code.\n * @param fallbackConfiguration Fallback to use when the {@link userProvidedConfiguration} does not specify an option.\n * @param defaultConfiguration The defaults as defined by the exporter specification\n */\nexport function mergeOtlpSharedConfigurationWithDefaults(\n  userProvidedConfiguration: Partial<OtlpSharedConfiguration>,\n  fallbackConfiguration: Partial<OtlpSharedConfiguration>,\n  defaultConfiguration: OtlpSharedConfiguration\n): OtlpSharedConfiguration {\n  return {\n    timeoutMillis: validateTimeoutMillis(\n      userProvidedConfiguration.timeoutMillis ??\n        fallbackConfiguration.timeoutMillis ??\n        defaultConfiguration.timeoutMillis\n    ),\n    concurrencyLimit:\n      userProvidedConfiguration.concurrencyLimit ??\n      fallbackConfiguration.concurrencyLimit ??\n      defaultConfiguration.concurrencyLimit,\n    compression:\n      userProvidedConfiguration.compression ??\n      fallbackConfiguration.compression ??\n      defaultConfiguration.compression,\n  };\n}\n\nexport function getSharedConfigurationDefaults(): OtlpSharedConfiguration {\n  return {\n    timeoutMillis: 10000,\n    concurrencyLimit: 30,\n    compression: 'none',\n  };\n}\n"]}