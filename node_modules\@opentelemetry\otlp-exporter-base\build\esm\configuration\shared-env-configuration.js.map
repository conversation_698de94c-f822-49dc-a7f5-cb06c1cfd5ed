{"version": 3, "file": "shared-env-configuration.js", "sourceRoot": "", "sources": ["../../../src/configuration/shared-env-configuration.ts"], "names": [], "mappings": "AAgBA,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,SAAS,8BAA8B,CACrC,aAAqB;IAErB,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,CAAC;IACtD,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,KAAK,EAAE,EAAE;QAC3C,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,cAAc,GAAG,CAAC,EAAE;YACzD,OAAO,cAAc,CAAC;SACvB;QACD,IAAI,CAAC,IAAI,CACP,kBAAkB,aAAa,wDAAwD,UAAU,GAAG,CACrG,CAAC;KACH;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,iBAAiB,CAAC,gBAAwB;IACjD,MAAM,eAAe,GAAG,8BAA8B,CACpD,sBAAsB,gBAAgB,UAAU,CACjD,CAAC;IACF,MAAM,kBAAkB,GAAG,8BAA8B,CACvD,4BAA4B,CAC7B,CAAC;IAEF,OAAO,eAAe,IAAI,kBAAkB,CAAC;AAC/C,CAAC;AAED,SAAS,kCAAkC,CACzC,iBAAyB;IAEzB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,CAAC;IAC3D,IAAI,WAAW,KAAK,EAAE,EAAE;QACtB,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,WAAW,IAAI,IAAI,IAAI,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK,MAAM,EAAE;QAC3E,OAAO,WAAW,CAAC;KACpB;IAED,IAAI,CAAC,IAAI,CACP,kBAAkB,iBAAiB,oDAAoD,WAAW,IAAI,CACvG,CAAC;IACF,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,qBAAqB,CAC5B,gBAAwB;IAExB,MAAM,mBAAmB,GAAG,kCAAkC,CAC5D,sBAAsB,gBAAgB,cAAc,CACrD,CAAC;IACF,MAAM,sBAAsB,GAAG,kCAAkC,CAC/D,gCAAgC,CACjC,CAAC;IAEF,OAAO,mBAAmB,IAAI,sBAAsB,CAAC;AACvD,CAAC;AAED,MAAM,UAAU,qCAAqC,CACnD,gBAAwB;IAExB,OAAO;QACL,aAAa,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;QAClD,WAAW,EAAE,qBAAqB,CAAC,gBAAgB,CAAC;KACrD,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { OtlpSharedConfiguration } from './shared-configuration';\nimport { diag } from '@opentelemetry/api';\n\nfunction parseAndValidateTimeoutFromEnv(\n  timeoutEnvVar: string\n): number | undefined {\n  const envTimeout = process.env[timeoutEnvVar]?.trim();\n  if (envTimeout != null && envTimeout !== '') {\n    const definedTimeout = Number(envTimeout);\n    if (Number.isFinite(definedTimeout) && definedTimeout > 0) {\n      return definedTimeout;\n    }\n    diag.warn(\n      `Configuration: ${timeoutEnvVar} is invalid, expected number greater than 0 (actual: ${envTimeout})`\n    );\n  }\n  return undefined;\n}\n\nfunction getTimeoutFromEnv(signalIdentifier: string) {\n  const specificTimeout = parseAndValidateTimeoutFromEnv(\n    `OTEL_EXPORTER_OTLP_${signalIdentifier}_TIMEOUT`\n  );\n  const nonSpecificTimeout = parseAndValidateTimeoutFromEnv(\n    'OTEL_EXPORTER_OTLP_TIMEOUT'\n  );\n\n  return specificTimeout ?? nonSpecificTimeout;\n}\n\nfunction parseAndValidateCompressionFromEnv(\n  compressionEnvVar: string\n): 'none' | 'gzip' | undefined {\n  const compression = process.env[compressionEnvVar]?.trim();\n  if (compression === '') {\n    return undefined;\n  }\n\n  if (compression == null || compression === 'none' || compression === 'gzip') {\n    return compression;\n  }\n\n  diag.warn(\n    `Configuration: ${compressionEnvVar} is invalid, expected 'none' or 'gzip' (actual: '${compression}')`\n  );\n  return undefined;\n}\n\nfunction getCompressionFromEnv(\n  signalIdentifier: string\n): 'none' | 'gzip' | undefined {\n  const specificCompression = parseAndValidateCompressionFromEnv(\n    `OTEL_EXPORTER_OTLP_${signalIdentifier}_COMPRESSION`\n  );\n  const nonSpecificCompression = parseAndValidateCompressionFromEnv(\n    'OTEL_EXPORTER_OTLP_COMPRESSION'\n  );\n\n  return specificCompression ?? nonSpecificCompression;\n}\n\nexport function getSharedConfigurationFromEnvironment(\n  signalIdentifier: string\n): Partial<OtlpSharedConfiguration> {\n  return {\n    timeoutMillis: getTimeoutFromEnv(signalIdentifier),\n    compression: getCompressionFromEnv(signalIdentifier),\n  };\n}\n"]}