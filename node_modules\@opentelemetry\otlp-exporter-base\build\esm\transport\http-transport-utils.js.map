{"version": 3, "file": "http-transport-utils.js", "sourceRoot": "", "sources": ["../../../src/transport/http-transport-utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAGlC,OAAO,EACL,iBAAiB,EACjB,sBAAsB,GACvB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAE7C;;;;;;;GAOG;AACH,MAAM,UAAU,YAAY,CAC1B,MAA6B,EAC7B,KAA+B,EAC/B,IAAgB,EAChB,MAA0C,EAC1C,aAAqB;IAErB,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACtC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhE,MAAM,OAAO,GAA+C;QAC1D,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,IAAI,EAAE,SAAS,CAAC,QAAQ;QACxB,MAAM,EAAE,MAAM;QACd,OAAO,EAAE;YACP,GAAG,MAAM,CAAC,OAAO,EAAE;SACpB;QACD,KAAK,EAAE,KAAK;KACb,CAAC;IAEF,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;IAE9E,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAyB,EAAE,EAAE;QACzD,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAElD,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACjB,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE;gBAC1C,MAAM,CAAC;oBACL,MAAM,EAAE,SAAS;oBACjB,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;iBAClC,CAAC,CAAC;aACJ;iBAAM,IAAI,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBAC9D,MAAM,CAAC;oBACL,MAAM,EAAE,WAAW;oBACnB,aAAa,EAAE,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;iBAClE,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,KAAK,GAAG,IAAI,iBAAiB,CACjC,GAAG,CAAC,aAAa,EACjB,GAAG,CAAC,UAAU,EACd,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,CACvC,CAAC;gBACF,MAAM,CAAC;oBACL,MAAM,EAAE,SAAS;oBACjB,KAAK;iBACN,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG,EAAE;QACjC,GAAG,CAAC,OAAO,EAAE,CAAC;QACd,MAAM,CAAC;YACL,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,IAAI,KAAK,CAAC,iBAAiB,CAAC;SACpC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;QAC/B,MAAM,CAAC;YACL,MAAM,EAAE,SAAS;YACjB,KAAK;SACN,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,uBAAuB,GAAG,WAAW,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;IACtE,GAAG,CAAC,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACnC,MAAM,CAAC;YACL,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC;SACtC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,KAAY,EAAE,EAAE;QAC9D,MAAM,CAAC;YACL,MAAM,EAAE,SAAS;YACjB,KAAK;SACN,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,GAAuB,EACvB,WAA4B,EAC5B,IAAgB,EAChB,OAA+B;IAE/B,IAAI,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAI,WAAW,KAAK,MAAM,EAAE;QAC1B,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAC1C,UAAU,GAAG,UAAU;aACpB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;aACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;aACvB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACzB;IAED,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAyB;IACvD,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAChC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEpB,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,MAAc,EACd,YAAoD;IAEpD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;IAClC,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;IACxE,OAAO,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AACjC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as http from 'http';\nimport * as https from 'https';\nimport * as zlib from 'zlib';\nimport { Readable } from 'stream';\nimport { HttpRequestParameters } from './http-transport-types';\nimport { ExportResponse } from '../export-response';\nimport {\n  isExportRetryable,\n  parseRetryAfterToMills,\n} from '../is-export-retryable';\nimport { OTLPExporterError } from '../types';\n\n/**\n * Sends data using http\n * @param params\n * @param agent\n * @param data\n * @param onDone\n * @param timeoutMillis\n */\nexport function sendWithHttp(\n  params: HttpRequestParameters,\n  agent: http.Agent | https.Agent,\n  data: Uint8Array,\n  onDone: (response: ExportResponse) => void,\n  timeoutMillis: number\n): void {\n  const parsedUrl = new URL(params.url);\n  const nodeVersion = Number(process.versions.node.split('.')[0]);\n\n  const options: http.RequestOptions | https.RequestOptions = {\n    hostname: parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname,\n    method: 'POST',\n    headers: {\n      ...params.headers(),\n    },\n    agent: agent,\n  };\n\n  const request = parsedUrl.protocol === 'http:' ? http.request : https.request;\n\n  const req = request(options, (res: http.IncomingMessage) => {\n    const responseData: Buffer[] = [];\n    res.on('data', chunk => responseData.push(chunk));\n\n    res.on('end', () => {\n      if (res.statusCode && res.statusCode < 299) {\n        onDone({\n          status: 'success',\n          data: Buffer.concat(responseData),\n        });\n      } else if (res.statusCode && isExportRetryable(res.statusCode)) {\n        onDone({\n          status: 'retryable',\n          retryInMillis: parseRetryAfterToMills(res.headers['retry-after']),\n        });\n      } else {\n        const error = new OTLPExporterError(\n          res.statusMessage,\n          res.statusCode,\n          Buffer.concat(responseData).toString()\n        );\n        onDone({\n          status: 'failure',\n          error,\n        });\n      }\n    });\n  });\n\n  req.setTimeout(timeoutMillis, () => {\n    req.destroy();\n    onDone({\n      status: 'failure',\n      error: new Error('Request Timeout'),\n    });\n  });\n  req.on('error', (error: Error) => {\n    onDone({\n      status: 'failure',\n      error,\n    });\n  });\n\n  const reportTimeoutErrorEvent = nodeVersion >= 14 ? 'close' : 'abort';\n  req.on(reportTimeoutErrorEvent, () => {\n    onDone({\n      status: 'failure',\n      error: new Error('Request timed out'),\n    });\n  });\n\n  compressAndSend(req, params.compression, data, (error: Error) => {\n    onDone({\n      status: 'failure',\n      error,\n    });\n  });\n}\n\nexport function compressAndSend(\n  req: http.ClientRequest,\n  compression: 'gzip' | 'none',\n  data: Uint8Array,\n  onError: (error: Error) => void\n) {\n  let dataStream = readableFromUint8Array(data);\n\n  if (compression === 'gzip') {\n    req.setHeader('Content-Encoding', 'gzip');\n    dataStream = dataStream\n      .on('error', onError)\n      .pipe(zlib.createGzip())\n      .on('error', onError);\n  }\n\n  dataStream.pipe(req).on('error', onError);\n}\n\nfunction readableFromUint8Array(buff: string | Uint8Array): Readable {\n  const readable = new Readable();\n  readable.push(buff);\n  readable.push(null);\n\n  return readable;\n}\n\nexport function createHttpAgent(\n  rawUrl: string,\n  agentOptions: http.AgentOptions | https.AgentOptions\n) {\n  const parsedUrl = new URL(rawUrl);\n  const Agent = parsedUrl.protocol === 'http:' ? http.Agent : https.Agent;\n  return new Agent(agentOptions);\n}\n"]}