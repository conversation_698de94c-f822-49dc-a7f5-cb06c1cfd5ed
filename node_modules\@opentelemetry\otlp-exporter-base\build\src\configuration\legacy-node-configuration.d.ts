/// <reference types="node" />
/// <reference types="node" />
import type * as http from 'http';
import type * as https from 'https';
import { OTLPExporterConfigBase } from './legacy-base-configuration';
/**
 * Collector Exporter node base config
 */
export interface OTLPExporterNodeConfigBase extends OTLPExporterConfigBase {
    keepAlive?: boolean;
    compression?: CompressionAlgorithm;
    httpAgentOptions?: http.AgentOptions | https.AgentOptions;
}
export declare enum CompressionAlgorithm {
    NONE = "none",
    GZIP = "gzip"
}
//# sourceMappingURL=legacy-node-configuration.d.ts.map