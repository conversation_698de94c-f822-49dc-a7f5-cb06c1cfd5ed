import { OtlpHttpConfiguration } from './otlp-http-configuration';
/**
 * Reads and returns configuration from the environment
 *
 * @param signalIdentifier all caps part in environment variables that identifies the signal (e.g.: METRICS, TRACES, LOGS)
 * @param signalResourcePath signal resource path to append if necessary (e.g.: v1/metrics, v1/traces, v1/logs)
 */
export declare function getHttpConfigurationFromEnvironment(signalIdentifier: string, signalResourcePath: string): Partial<OtlpHttpConfiguration>;
//# sourceMappingURL=otlp-http-env-configuration.d.ts.map