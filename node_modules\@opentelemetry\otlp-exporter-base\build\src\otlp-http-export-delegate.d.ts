import { IOtlpExportDelegate } from './otlp-export-delegate';
import { OtlpHttpConfiguration } from './configuration/otlp-http-configuration';
import { ISerializer } from '@opentelemetry/otlp-transformer';
export declare function createOtlpHttpExportDelegate<Internal, Response>(options: OtlpHttpConfiguration, serializer: ISerializer<Internal, Response>): IOtlpExportDelegate<Internal>;
//# sourceMappingURL=otlp-http-export-delegate.d.ts.map