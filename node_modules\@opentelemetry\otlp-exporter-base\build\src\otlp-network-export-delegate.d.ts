import { OtlpSharedConfiguration } from './configuration/shared-configuration';
import { ISerializer } from '@opentelemetry/otlp-transformer';
import { IExporterTransport } from './exporter-transport';
import { IOtlpExportDelegate } from './otlp-export-delegate';
export declare function createOtlpNetworkExportDelegate<Internal, Response>(options: OtlpSharedConfiguration, serializer: ISerializer<Internal, Response>, transport: IExporterTransport): IOtlpExportDelegate<Internal>;
//# sourceMappingURL=otlp-network-export-delegate.d.ts.map