/**
 * Interface for handling error
 */
export declare class OTLPExporterError extends Error {
    readonly code?: number;
    readonly name: string;
    readonly data?: string;
    constructor(message?: string, code?: number, data?: string);
}
/**
 * Interface for handling export service errors
 */
export interface ExportServiceError {
    name: string;
    code: number;
    details: string;
    metadata: {
        [key: string]: unknown;
    };
    message: string;
    stack: string;
}
//# sourceMappingURL=types.d.ts.map