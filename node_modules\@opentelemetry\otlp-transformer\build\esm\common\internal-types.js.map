{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../../src/common/internal-types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Properties of a Resource. */\nexport interface Resource {\n  /** Resource attributes */\n  attributes: IKeyValue[];\n\n  /** Resource droppedAttributesCount */\n  droppedAttributesCount: number;\n}\n\n/** Properties of an InstrumentationScope. */\nexport interface IInstrumentationScope {\n  /** InstrumentationScope name */\n  name: string;\n\n  /** InstrumentationScope version */\n  version?: string;\n\n  /** InstrumentationScope attributes */\n  attributes?: IKeyValue[];\n\n  /** InstrumentationScope droppedAttributesCount */\n  droppedAttributesCount?: number;\n}\n\n/** Properties of a KeyValue. */\nexport interface IKeyValue {\n  /** KeyValue key */\n  key: string;\n\n  /** KeyValue value */\n  value: IAnyValue;\n}\n\n/** Properties of an AnyValue. */\nexport interface IAnyValue {\n  /** AnyValue stringValue */\n  stringValue?: string | null;\n\n  /** AnyValue boolValue */\n  boolValue?: boolean | null;\n\n  /** AnyValue intValue */\n  intValue?: number | null;\n\n  /** AnyValue doubleValue */\n  doubleValue?: number | null;\n\n  /** AnyValue arrayValue */\n  arrayValue?: IArrayValue;\n\n  /** AnyValue kvlistValue */\n  kvlistValue?: IKeyValueList;\n\n  /** AnyValue bytesValue */\n  bytesValue?: Uint8Array;\n}\n\n/** Properties of an ArrayValue. */\nexport interface IArrayValue {\n  /** ArrayValue values */\n  values: IAnyValue[];\n}\n\n/** Properties of a KeyValueList. */\nexport interface IKeyValueList {\n  /** KeyValueList values */\n  values: IKeyValue[];\n}\n\nexport interface LongBits {\n  low: number;\n  high: number;\n}\n\nexport type Fixed64 = LongBits | string | number;\n\nexport interface OtlpEncodingOptions {\n  /** Convert trace and span IDs to hex strings. */\n  useHex?: boolean;\n  /** Convert HrTime to 2 part 64 bit values. */\n  useLongBits?: boolean;\n}\n"]}