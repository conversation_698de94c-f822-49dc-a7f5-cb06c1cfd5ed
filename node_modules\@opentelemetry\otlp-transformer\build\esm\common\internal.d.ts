import type { IAnyValue, IInstrumentationScope, IKeyValue, Resource } from './internal-types';
import { Attributes } from '@opentelemetry/api';
import { InstrumentationScope } from '@opentelemetry/core';
import { Resource as ISdkResource } from '@opentelemetry/resources';
export declare function createResource(resource: ISdkResource): Resource;
export declare function createInstrumentationScope(scope: InstrumentationScope): IInstrumentationScope;
export declare function toAttributes(attributes: Attributes): IKeyValue[];
export declare function toKeyValue(key: string, value: unknown): IKeyValue;
export declare function toAnyValue(value: unknown): IAnyValue;
//# sourceMappingURL=internal.d.ts.map