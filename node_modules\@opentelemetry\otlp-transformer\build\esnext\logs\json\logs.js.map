{"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["../../../../src/logs/json/logs.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,8BAA8B,EAAE,MAAM,aAAa,CAAC;AAG7D;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAG3B;IACF,gBAAgB,EAAE,CAAC,GAAwB,EAAE,EAAE;QAC7C,MAAM,OAAO,GAAG,8BAA8B,CAAC,GAAG,EAAE;YAClD,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,EAAE,CAAC;SACX;QACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAA+B,CAAC;IACvE,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ISerializer } from '../../i-serializer';\nimport { ReadableLogRecord } from '@opentelemetry/sdk-logs';\nimport { createExportLogsServiceRequest } from '../internal';\nimport { IExportLogsServiceResponse } from '../export-response';\n\n/*\n * @experimental this serializer may receive breaking changes in minor versions, pin this package's version when using this constant\n */\nexport const JsonLogsSerializer: ISerializer<\n  ReadableLogRecord[],\n  IExportLogsServiceResponse\n> = {\n  serializeRequest: (arg: ReadableLogRecord[]) => {\n    const request = createExportLogsServiceRequest(arg, {\n      useHex: true,\n      useLongBits: false,\n    });\n    const encoder = new TextEncoder();\n    return encoder.encode(JSON.stringify(request));\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    if (arg.length === 0) {\n      return {};\n    }\n    const decoder = new TextDecoder();\n    return JSON.parse(decoder.decode(arg)) as IExportLogsServiceResponse;\n  },\n};\n"]}