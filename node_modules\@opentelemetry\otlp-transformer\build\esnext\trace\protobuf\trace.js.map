{"version": 3, "file": "trace.js", "sourceRoot": "", "sources": ["../../../../src/trace/protobuf/trace.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,IAAI,MAAM,sBAAsB,CAAC;AAK7C,OAAO,EAAE,+BAA+B,EAAE,MAAM,aAAa,CAAC;AAG9D,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;KAClE,0BAAqE,CAAC;AAEzE,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;KACjE,yBAAmE,CAAC;AAEvE,MAAM,CAAC,MAAM,uBAAuB,GAGhC;IACF,gBAAgB,EAAE,CAAC,GAAmB,EAAE,EAAE;QACxC,MAAM,OAAO,GAAG,+BAA+B,CAAC,GAAG,CAAC,CAAC;QACrD,OAAO,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IACnD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,OAAO,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from '../../generated/root';\nimport { ISerializer } from '../../i-serializer';\nimport { ExportType } from '../../common/protobuf/protobuf-export-type';\nimport { IExportTraceServiceRequest } from '../internal-types';\nimport { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { createExportTraceServiceRequest } from '../internal';\nimport { IExportTraceServiceResponse } from '../export-response';\n\nconst traceResponseType = root.opentelemetry.proto.collector.trace.v1\n  .ExportTraceServiceResponse as ExportType<IExportTraceServiceResponse>;\n\nconst traceRequestType = root.opentelemetry.proto.collector.trace.v1\n  .ExportTraceServiceRequest as ExportType<IExportTraceServiceRequest>;\n\nexport const ProtobufTraceSerializer: ISerializer<\n  ReadableSpan[],\n  IExportTraceServiceResponse\n> = {\n  serializeRequest: (arg: ReadableSpan[]) => {\n    const request = createExportTraceServiceRequest(arg);\n    return traceRequestType.encode(request).finish();\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    return traceResponseType.decode(arg);\n  },\n};\n"]}