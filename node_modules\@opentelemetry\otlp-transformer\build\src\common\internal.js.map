{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/common/internal.ts"], "names": [], "mappings": ";;;AAyBA,SAAgB,cAAc,CAAC,QAAsB;IACnD,OAAO;QACL,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC7C,sBAAsB,EAAE,CAAC;KAC1B,CAAC;AACJ,CAAC;AALD,wCAKC;AAED,SAAgB,0BAA0B,CACxC,KAA2B;IAE3B,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC;AACJ,CAAC;AAPD,gEAOC;AAED,SAAgB,YAAY,CAAC,UAAsB;IACjD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC;AAFD,oCAEC;AAED,SAAgB,UAAU,CAAC,GAAW,EAAE,KAAc;IACpD,OAAO;QACL,GAAG,EAAE,GAAG;QACR,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC;KACzB,CAAC;AACJ,CAAC;AALD,gCAKC;AAED,SAAgB,UAAU,CAAC,KAAc;IACvC,MAAM,CAAC,GAAG,OAAO,KAAK,CAAC;IACvB,IAAI,CAAC,KAAK,QAAQ;QAAE,OAAO,EAAE,WAAW,EAAE,KAAe,EAAE,CAAC;IAC5D,IAAI,CAAC,KAAK,QAAQ,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;YAAE,OAAO,EAAE,WAAW,EAAE,KAAe,EAAE,CAAC;QACtE,OAAO,EAAE,QAAQ,EAAE,KAAe,EAAE,CAAC;KACtC;IACD,IAAI,CAAC,KAAK,SAAS;QAAE,OAAO,EAAE,SAAS,EAAE,KAAgB,EAAE,CAAC;IAC5D,IAAI,KAAK,YAAY,UAAU;QAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACtB,OAAO,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;IAC3D,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI;QACjC,OAAO;YACL,WAAW,EAAE;gBACX,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,KAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CACrD,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CACjB;aACF;SACF,CAAC;IAEJ,OAAO,EAAE,CAAC;AACZ,CAAC;AArBD,gCAqBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type {\n  IAnyValue,\n  IInstrumentationScope,\n  IKeyValue,\n  Resource,\n} from './internal-types';\nimport { Attributes } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { Resource as ISdkResource } from '@opentelemetry/resources';\n\nexport function createResource(resource: ISdkResource): Resource {\n  return {\n    attributes: toAttributes(resource.attributes),\n    droppedAttributesCount: 0,\n  };\n}\n\nexport function createInstrumentationScope(\n  scope: InstrumentationScope\n): IInstrumentationScope {\n  return {\n    name: scope.name,\n    version: scope.version,\n  };\n}\n\nexport function toAttributes(attributes: Attributes): IKeyValue[] {\n  return Object.keys(attributes).map(key => toKeyValue(key, attributes[key]));\n}\n\nexport function toKeyValue(key: string, value: unknown): IKeyValue {\n  return {\n    key: key,\n    value: toAnyValue(value),\n  };\n}\n\nexport function toAnyValue(value: unknown): IAnyValue {\n  const t = typeof value;\n  if (t === 'string') return { stringValue: value as string };\n  if (t === 'number') {\n    if (!Number.isInteger(value)) return { doubleValue: value as number };\n    return { intValue: value as number };\n  }\n  if (t === 'boolean') return { boolValue: value as boolean };\n  if (value instanceof Uint8Array) return { bytesValue: value };\n  if (Array.isArray(value))\n    return { arrayValue: { values: value.map(toAnyValue) } };\n  if (t === 'object' && value != null)\n    return {\n      kvlistValue: {\n        values: Object.entries(value as object).map(([k, v]) =>\n          toKeyValue(k, v)\n        ),\n      },\n    };\n\n  return {};\n}\n"]}