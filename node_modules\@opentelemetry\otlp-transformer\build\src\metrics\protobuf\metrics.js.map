{"version": 3, "file": "metrics.js", "sourceRoot": "", "sources": ["../../../../src/metrics/protobuf/metrics.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,6CAA6C;AAI7C,0CAAgE;AAIhE,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;KACtE,4BAAyE,CAAC;AAE7E,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;KACrE,2BAAuE,CAAC;AAE9D,QAAA,yBAAyB,GAGlC;IACF,gBAAgB,EAAE,CAAC,GAAoB,EAAE,EAAE;QACzC,MAAM,OAAO,GAAG,IAAA,4CAAiC,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACzD,OAAO,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IACrD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,OAAO,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from '../../generated/root';\nimport { ISerializer } from '../../i-serializer';\nimport { IExportMetricsServiceRequest } from '../internal-types';\nimport { ExportType } from '../../common/protobuf/protobuf-export-type';\nimport { createExportMetricsServiceRequest } from '../internal';\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport { IExportMetricsServiceResponse } from '../export-response';\n\nconst metricsResponseType = root.opentelemetry.proto.collector.metrics.v1\n  .ExportMetricsServiceResponse as ExportType<IExportMetricsServiceResponse>;\n\nconst metricsRequestType = root.opentelemetry.proto.collector.metrics.v1\n  .ExportMetricsServiceRequest as ExportType<IExportMetricsServiceRequest>;\n\nexport const ProtobufMetricsSerializer: ISerializer<\n  ResourceMetrics,\n  IExportMetricsServiceResponse\n> = {\n  serializeRequest: (arg: ResourceMetrics) => {\n    const request = createExportMetricsServiceRequest([arg]);\n    return metricsRequestType.encode(request).finish();\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    return metricsResponseType.decode(arg);\n  },\n};\n"]}