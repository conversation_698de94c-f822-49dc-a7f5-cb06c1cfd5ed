{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/trace/internal.ts"], "names": [], "mappings": ";;;AAmBA,iDAI4B;AAW5B,2CAAiD;AAEjD,SAAgB,iBAAiB,CAAC,IAAkB,EAAE,OAAgB;IACpE,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,MAAM;QACjD,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC;QAC3D,CAAC,CAAC,SAAS,CAAC;IACd,OAAO;QACL,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;QAC/C,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC;QAC7C,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE;QACvC,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,6EAA6E;QAC7E,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;QAC3C,iBAAiB,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;QACvD,eAAe,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;QACnD,UAAU,EAAE,IAAA,uBAAY,EAAC,IAAI,CAAC,UAAU,CAAC;QACzC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;QACnD,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;QAC3C,MAAM,EAAE;YACN,4CAA4C;YAC5C,IAAI,EAAE,MAAM,CAAC,IAA8B;YAC3C,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB;QACD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACxD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;KAC1C,CAAC;AACJ,CAAC;AA5BD,8CA4BC;AAED,SAAgB,UAAU,CAAC,IAAU,EAAE,OAAgB;IACrD,OAAO;QACL,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAA,uBAAY,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAChE,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACtD,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACxD,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE;QAChD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,IAAI,CAAC;KACzD,CAAC;AACJ,CAAC;AARD,gCAQC;AAED,SAAgB,eAAe,CAC7B,UAAsB,EACtB,OAAgB;IAEhB,OAAO;QACL,UAAU,EAAE,UAAU,CAAC,UAAU;YAC/B,CAAC,CAAC,IAAA,uBAAY,EAAC,UAAU,CAAC,UAAU,CAAC;YACrC,CAAC,CAAC,EAAE;QACN,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC;QACnD,sBAAsB,EAAE,UAAU,CAAC,sBAAsB,IAAI,CAAC;KAC/D,CAAC;AACJ,CAAC;AAZD,0CAYC;AAED;;;;;;;;;;;;;;GAcG;AAEH,SAAgB,+BAA+B,CAC7C,KAAqB,EACrB,OAA6B;IAE7B,MAAM,OAAO,GAAG,IAAA,sBAAc,EAAC,OAAO,CAAC,CAAC;IACxC,OAAO;QACL,aAAa,EAAE,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC;KAC1D,CAAC;AACJ,CAAC;AARD,0EAQC;AAED,SAAS,iBAAiB,CAAC,aAA6B;IACtD,MAAM,WAAW,GAA+C,IAAI,GAAG,EAAE,CAAC;IAC1E,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE;QAClC,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACnB,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAC1C;QAED,kFAAkF;QAClF,MAAM,uBAAuB,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,IAAI,IACjE,MAAM,CAAC,oBAAoB,CAAC,OAAO,IAAI,EACzC,IAAI,MAAM,CAAC,oBAAoB,CAAC,SAAS,IAAI,EAAE,EAAE,CAAC;QAClD,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;SAC9C;QAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtB;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,0BAA0B,CACjC,aAA6B,EAC7B,OAAgB;IAEhB,MAAM,WAAW,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;IACrD,MAAM,GAAG,GAAqB,EAAE,CAAC;IAEjC,MAAM,aAAa,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;IAC5C,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;IACjC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;QAClB,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACvC,MAAM,kBAAkB,GAAkB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QACpC,IAAI,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;QAClC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;YACrB,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;YAClC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAC1C,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC,CACzC,CAAC;gBAEF,kBAAkB,CAAC,IAAI,CAAC;oBACtB,KAAK,EAAE,IAAA,qCAA0B,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;oBACrE,KAAK,EAAE,KAAK;oBACZ,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,SAAS;iBACxD,CAAC,CAAC;aACJ;YACD,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;SAC/B;QACD,gEAAgE;QAChE,MAAM,gBAAgB,GAAmB;YACvC,QAAQ,EAAE,IAAA,yBAAc,EAAC,QAAQ,CAAC;YAClC,UAAU,EAAE,kBAAkB;YAC9B,SAAS,EAAE,SAAS;SACrB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3B,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;KAC9B;IAED,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { Link } from '@opentelemetry/api';\nimport { Resource } from '@opentelemetry/resources';\nimport type { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';\nimport type { Encoder } from '../common/utils';\nimport {\n  createInstrumentationScope,\n  createResource,\n  toAttributes,\n} from '../common/internal';\nimport {\n  EStatusCode,\n  IEvent,\n  IExportTraceServiceRequest,\n  ILink,\n  IResourceSpans,\n  IScopeSpans,\n  ISpan,\n} from './internal-types';\nimport { OtlpEncodingOptions } from '../common/internal-types';\nimport { getOtlpEncoder } from '../common/utils';\n\nexport function sdkSpanToOtlpSpan(span: ReadableSpan, encoder: Encoder): ISpan {\n  const ctx = span.spanContext();\n  const status = span.status;\n  const parentSpanId = span.parentSpanContext?.spanId\n    ? encoder.encodeSpanContext(span.parentSpanContext?.spanId)\n    : undefined;\n  return {\n    traceId: encoder.encodeSpanContext(ctx.traceId),\n    spanId: encoder.encodeSpanContext(ctx.spanId),\n    parentSpanId: parentSpanId,\n    traceState: ctx.traceState?.serialize(),\n    name: span.name,\n    // Span kind is offset by 1 because the API does not define a value for unset\n    kind: span.kind == null ? 0 : span.kind + 1,\n    startTimeUnixNano: encoder.encodeHrTime(span.startTime),\n    endTimeUnixNano: encoder.encodeHrTime(span.endTime),\n    attributes: toAttributes(span.attributes),\n    droppedAttributesCount: span.droppedAttributesCount,\n    events: span.events.map(event => toOtlpSpanEvent(event, encoder)),\n    droppedEventsCount: span.droppedEventsCount,\n    status: {\n      // API and proto enums share the same values\n      code: status.code as unknown as EStatusCode,\n      message: status.message,\n    },\n    links: span.links.map(link => toOtlpLink(link, encoder)),\n    droppedLinksCount: span.droppedLinksCount,\n  };\n}\n\nexport function toOtlpLink(link: Link, encoder: Encoder): ILink {\n  return {\n    attributes: link.attributes ? toAttributes(link.attributes) : [],\n    spanId: encoder.encodeSpanContext(link.context.spanId),\n    traceId: encoder.encodeSpanContext(link.context.traceId),\n    traceState: link.context.traceState?.serialize(),\n    droppedAttributesCount: link.droppedAttributesCount || 0,\n  };\n}\n\nexport function toOtlpSpanEvent(\n  timedEvent: TimedEvent,\n  encoder: Encoder\n): IEvent {\n  return {\n    attributes: timedEvent.attributes\n      ? toAttributes(timedEvent.attributes)\n      : [],\n    name: timedEvent.name,\n    timeUnixNano: encoder.encodeHrTime(timedEvent.time),\n    droppedAttributesCount: timedEvent.droppedAttributesCount || 0,\n  };\n}\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function createExportTraceServiceRequest(\n  spans: ReadableSpan[],\n  options?: OtlpEncodingOptions\n): IExportTraceServiceRequest {\n  const encoder = getOtlpEncoder(options);\n  return {\n    resourceSpans: spanRecordsToResourceSpans(spans, encoder),\n  };\n}\n\nfunction createResourceMap(readableSpans: ReadableSpan[]) {\n  const resourceMap: Map<Resource, Map<string, ReadableSpan[]>> = new Map();\n  for (const record of readableSpans) {\n    let ilsMap = resourceMap.get(record.resource);\n\n    if (!ilsMap) {\n      ilsMap = new Map();\n      resourceMap.set(record.resource, ilsMap);\n    }\n\n    // TODO this is duplicated in basic tracer. Consolidate on a common helper in core\n    const instrumentationScopeKey = `${record.instrumentationScope.name}@${\n      record.instrumentationScope.version || ''\n    }:${record.instrumentationScope.schemaUrl || ''}`;\n    let records = ilsMap.get(instrumentationScopeKey);\n\n    if (!records) {\n      records = [];\n      ilsMap.set(instrumentationScopeKey, records);\n    }\n\n    records.push(record);\n  }\n\n  return resourceMap;\n}\n\nfunction spanRecordsToResourceSpans(\n  readableSpans: ReadableSpan[],\n  encoder: Encoder\n): IResourceSpans[] {\n  const resourceMap = createResourceMap(readableSpans);\n  const out: IResourceSpans[] = [];\n\n  const entryIterator = resourceMap.entries();\n  let entry = entryIterator.next();\n  while (!entry.done) {\n    const [resource, ilmMap] = entry.value;\n    const scopeResourceSpans: IScopeSpans[] = [];\n    const ilmIterator = ilmMap.values();\n    let ilmEntry = ilmIterator.next();\n    while (!ilmEntry.done) {\n      const scopeSpans = ilmEntry.value;\n      if (scopeSpans.length > 0) {\n        const spans = scopeSpans.map(readableSpan =>\n          sdkSpanToOtlpSpan(readableSpan, encoder)\n        );\n\n        scopeResourceSpans.push({\n          scope: createInstrumentationScope(scopeSpans[0].instrumentationScope),\n          spans: spans,\n          schemaUrl: scopeSpans[0].instrumentationScope.schemaUrl,\n        });\n      }\n      ilmEntry = ilmIterator.next();\n    }\n    // TODO SDK types don't provide resource schema URL at this time\n    const transformedSpans: IResourceSpans = {\n      resource: createResource(resource),\n      scopeSpans: scopeResourceSpans,\n      schemaUrl: undefined,\n    };\n\n    out.push(transformedSpans);\n    entry = entryIterator.next();\n  }\n\n  return out;\n}\n"]}