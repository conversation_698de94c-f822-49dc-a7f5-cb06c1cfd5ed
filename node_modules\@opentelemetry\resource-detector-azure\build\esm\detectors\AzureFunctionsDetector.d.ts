import { ResourceDetector, DetectedResource } from '@opentelemetry/resources';
/**
 * The AzureFunctionsDetector can be used to detect if a process is running in Azure Functions
 * @returns a {@link Resource} populated with data about the environment or an empty Resource if detection fails.
 */
declare class AzureFunctionsDetector implements ResourceDetector {
    detect(): DetectedResource;
}
export declare const azureFunctionsDetector: AzureFunctionsDetector;
export {};
//# sourceMappingURL=AzureFunctionsDetector.d.ts.map