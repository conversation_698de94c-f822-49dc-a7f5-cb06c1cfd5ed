import { Resource } from './Resource';
import { DetectedResource, DetectedResourceAttributes } from './types';
export declare function resourceFromAttributes(attributes: DetectedResourceAttributes): Resource;
export declare function resourceFromDetectedResource(detectedResource: DetectedResource): Resource;
export declare function emptyResource(): Resource;
export declare function defaultResource(): Resource;
//# sourceMappingURL=ResourceImpl.d.ts.map