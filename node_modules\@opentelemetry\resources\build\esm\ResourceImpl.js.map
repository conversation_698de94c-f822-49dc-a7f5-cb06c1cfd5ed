{"version": 3, "file": "ResourceImpl.js", "sourceRoot": "", "sources": ["../../src/ResourceImpl.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAA8B,IAAI,EAAE,MAAM,oBAAoB,CAAC;AACtE,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EACL,iBAAiB,EACjB,2BAA2B,EAC3B,uBAAuB,EACvB,0BAA0B,GAC3B,MAAM,qCAAqC,CAAC;AAE7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAOhD,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC,MAAM,YAAY;IACR,cAAc,CAAyB;IACvC,uBAAuB,GAAG,KAAK,CAAC;IAEhC,mBAAmB,CAAc;IAEzC,MAAM,CAAC,iBAAiB,CACtB,UAAgE;QAEhE,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,cAAc,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACtD,GAAG,CAAC,uBAAuB;YACzB,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QACjE,OAAO,GAAG,CAAC;IACb,CAAC;IAED;IACE;;;;OAIG;IACH,QAA0B;QAE1B,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YAC9D,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;gBACpB,cAAc;gBACd,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;aACrC;YAED,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClE,CAAC;IAED,IAAW,sBAAsB;QAC/B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,sBAAsB;QACjC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAChC,OAAO;SACR;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACvC,CAAC;IAED,IAAW,UAAU;QACnB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,IAAI,CAAC,KAAK,CACR,+DAA+D,CAChE,CAAC;SACH;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,OAAO,IAAI,CAAC,mBAAmB,CAAC;SACjC;QAED,MAAM,KAAK,GAAe,EAAE,CAAC;QAC7B,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;YACxC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;gBACxD,SAAS;aACV;YACD,IAAI,CAAC,IAAI,IAAI,EAAE;gBACb,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aAChB;SACF;QAED,oDAAoD;QACpD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAClC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,QAAyB;QACpC,IAAI,QAAQ,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC;QAElC,qBAAqB;QACrB,+DAA+D;QAC/D,OAAO,YAAY,CAAC,iBAAiB,CAAC;YACpC,GAAG,QAAQ,CAAC,gBAAgB,EAAE;YAC9B,GAAG,IAAI,CAAC,gBAAgB,EAAE;SAC3B,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,UAAU,sBAAsB,CACpC,UAAsC;IAEtC,OAAO,YAAY,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,gBAAkC;IAElC,OAAO,IAAI,YAAY,CAAC,gBAAgB,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,aAAa;IAC3B,OAAO,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,eAAe;IAC7B,OAAO,sBAAsB,CAAC;QAC5B,CAAC,iBAAiB,CAAC,EAAE,kBAAkB,EAAE;QACzC,CAAC,2BAA2B,CAAC,EAAE,QAAQ,CAAC,2BAA2B,CAAC;QACpE,CAAC,uBAAuB,CAAC,EAAE,QAAQ,CAAC,uBAAuB,CAAC;QAC5D,CAAC,0BAA0B,CAAC,EAAE,QAAQ,CAAC,0BAA0B,CAAC;KACnE,CAAC,CAAC;AACL,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAkC;IAElC,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QAC/B,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO;gBACL,CAAC;gBACD,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACZ,IAAI,CAAC,KAAK,CACR,mDAAmD,EACnD,CAAC,EACD,GAAG,CACJ,CAAC;oBACF,OAAO,SAAS,CAAC;gBACnB,CAAC,CAAC;aACH,CAAC;SACH;QACD,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes, AttributeValue, diag } from '@opentelemetry/api';\nimport { SDK_INFO } from '@opentelemetry/core';\nimport {\n  ATTR_SERVICE_NAME,\n  ATTR_TELEMETRY_SDK_LANGUAGE,\n  ATTR_TELEMETRY_SDK_NAME,\n  ATTR_TELEMETRY_SDK_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from './Resource';\nimport { defaultServiceName } from './platform';\nimport {\n  DetectedResource,\n  DetectedResourceAttributes,\n  MaybePromise,\n  RawResourceAttribute,\n} from './types';\nimport { isPromiseLike } from './utils';\n\nclass ResourceImpl implements Resource {\n  private _rawAttributes: RawResourceAttribute[];\n  private _asyncAttributesPending = false;\n\n  private _memoizedAttributes?: Attributes;\n\n  static FromAttributeList(\n    attributes: [string, MaybePromise<AttributeValue | undefined>][]\n  ): Resource {\n    const res = new ResourceImpl({});\n    res._rawAttributes = guardedRawAttributes(attributes);\n    res._asyncAttributesPending =\n      attributes.filter(([_, val]) => isPromiseLike(val)).length > 0;\n    return res;\n  }\n\n  constructor(\n    /**\n     * A dictionary of attributes with string keys and values that provide\n     * information about the entity as numbers, strings or booleans\n     * TODO: Consider to add check/validation on attributes.\n     */\n    resource: DetectedResource\n  ) {\n    const attributes = resource.attributes ?? {};\n    this._rawAttributes = Object.entries(attributes).map(([k, v]) => {\n      if (isPromiseLike(v)) {\n        // side-effect\n        this._asyncAttributesPending = true;\n      }\n\n      return [k, v];\n    });\n\n    this._rawAttributes = guardedRawAttributes(this._rawAttributes);\n  }\n\n  public get asyncAttributesPending(): boolean {\n    return this._asyncAttributesPending;\n  }\n\n  public async waitForAsyncAttributes(): Promise<void> {\n    if (!this.asyncAttributesPending) {\n      return;\n    }\n\n    for (let i = 0; i < this._rawAttributes.length; i++) {\n      const [k, v] = this._rawAttributes[i];\n      this._rawAttributes[i] = [k, isPromiseLike(v) ? await v : v];\n    }\n\n    this._asyncAttributesPending = false;\n  }\n\n  public get attributes(): Attributes {\n    if (this.asyncAttributesPending) {\n      diag.error(\n        'Accessing resource attributes before async attributes settled'\n      );\n    }\n\n    if (this._memoizedAttributes) {\n      return this._memoizedAttributes;\n    }\n\n    const attrs: Attributes = {};\n    for (const [k, v] of this._rawAttributes) {\n      if (isPromiseLike(v)) {\n        diag.debug(`Unsettled resource attribute ${k} skipped`);\n        continue;\n      }\n      if (v != null) {\n        attrs[k] ??= v;\n      }\n    }\n\n    // only memoize output if all attributes are settled\n    if (!this._asyncAttributesPending) {\n      this._memoizedAttributes = attrs;\n    }\n\n    return attrs;\n  }\n\n  public getRawAttributes(): RawResourceAttribute[] {\n    return this._rawAttributes;\n  }\n\n  public merge(resource: Resource | null): Resource {\n    if (resource == null) return this;\n\n    // Order is important\n    // Spec states incoming attributes override existing attributes\n    return ResourceImpl.FromAttributeList([\n      ...resource.getRawAttributes(),\n      ...this.getRawAttributes(),\n    ]);\n  }\n}\n\nexport function resourceFromAttributes(\n  attributes: DetectedResourceAttributes\n): Resource {\n  return ResourceImpl.FromAttributeList(Object.entries(attributes));\n}\n\nexport function resourceFromDetectedResource(\n  detectedResource: DetectedResource\n): Resource {\n  return new ResourceImpl(detectedResource);\n}\n\nexport function emptyResource(): Resource {\n  return resourceFromAttributes({});\n}\n\nexport function defaultResource(): Resource {\n  return resourceFromAttributes({\n    [ATTR_SERVICE_NAME]: defaultServiceName(),\n    [ATTR_TELEMETRY_SDK_LANGUAGE]: SDK_INFO[ATTR_TELEMETRY_SDK_LANGUAGE],\n    [ATTR_TELEMETRY_SDK_NAME]: SDK_INFO[ATTR_TELEMETRY_SDK_NAME],\n    [ATTR_TELEMETRY_SDK_VERSION]: SDK_INFO[ATTR_TELEMETRY_SDK_VERSION],\n  });\n}\n\nfunction guardedRawAttributes(\n  attributes: RawResourceAttribute[]\n): RawResourceAttribute[] {\n  return attributes.map(([k, v]) => {\n    if (isPromiseLike(v)) {\n      return [\n        k,\n        v.catch(err => {\n          diag.debug(\n            'promise rejection for resource attribute: %s - %s',\n            k,\n            err\n          );\n          return undefined;\n        }),\n      ];\n    }\n    return [k, v];\n  });\n}\n"]}