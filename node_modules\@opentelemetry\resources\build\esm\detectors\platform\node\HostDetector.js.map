{"version": 3, "file": "HostDetector.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/HostDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAChF,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAOpC,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH,MAAM,YAAY;IAChB,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAA+B;YAC7C,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE;YAC5B,CAAC,cAAc,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC;YACvC,CAAC,YAAY,CAAC,EAAE,YAAY,EAAE;SAC/B,CAAC;QAEF,OAAO,EAAE,UAAU,EAAE,CAAC;IACxB,CAAC;CACF;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ATTR_HOST_ARCH, ATTR_HOST_ID, ATTR_HOST_NAME } from '../../../semconv';\nimport { arch, hostname } from 'os';\nimport { ResourceDetectionConfig } from '../../../config';\nimport {\n  DetectedResource,\n  DetectedResourceAttributes,\n  ResourceDetector,\n} from '../../../types';\nimport { getMachineId } from './machine-id/getMachineId';\nimport { normalizeArch } from './utils';\n\n/**\n * HostDetector detects the resources related to the host current process is\n * running on. Currently only non-cloud-based attributes are included.\n */\nclass HostDetector implements ResourceDetector {\n  detect(_config?: ResourceDetectionConfig): DetectedResource {\n    const attributes: DetectedResourceAttributes = {\n      [ATTR_HOST_NAME]: hostname(),\n      [ATTR_HOST_ARCH]: normalizeArch(arch()),\n      [ATTR_HOST_ID]: getMachineId(),\n    };\n\n    return { attributes };\n  }\n}\n\nexport const hostDetector = new HostDetector();\n"]}