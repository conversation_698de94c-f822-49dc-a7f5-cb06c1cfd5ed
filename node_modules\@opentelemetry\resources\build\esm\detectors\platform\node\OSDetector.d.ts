import { ResourceDetectionConfig } from '../../../config';
import { DetectedResource, ResourceDetector } from '../../../types';
/**
 * OSDetector detects the resources related to the operating system (OS) on
 * which the process represented by this resource is running.
 */
declare class OSDetector implements ResourceDetector {
    detect(_config?: ResourceDetectionConfig): DetectedResource;
}
export declare const osDetector: OSDetector;
export {};
//# sourceMappingURL=OSDetector.d.ts.map