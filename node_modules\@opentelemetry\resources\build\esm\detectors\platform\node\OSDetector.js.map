{"version": 3, "file": "OSDetector.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/OSDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACjE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AAGvC,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH,MAAM,UAAU;IACd,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAe;YAC7B,CAAC,YAAY,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC;YACzC,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE;SAC7B,CAAC;QACF,OAAO,EAAE,UAAU,EAAE,CAAC;IACxB,CAAC;CACF;AAED,MAAM,CAAC,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes } from '@opentelemetry/api';\nimport { ATTR_OS_TYPE, ATTR_OS_VERSION } from '../../../semconv';\nimport { platform, release } from 'os';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { DetectedResource, ResourceDetector } from '../../../types';\nimport { normalizeType } from './utils';\n\n/**\n * OSDetector detects the resources related to the operating system (OS) on\n * which the process represented by this resource is running.\n */\nclass OSDetector implements ResourceDetector {\n  detect(_config?: ResourceDetectionConfig): DetectedResource {\n    const attributes: Attributes = {\n      [ATTR_OS_TYPE]: normalizeType(platform()),\n      [ATTR_OS_VERSION]: release(),\n    };\n    return { attributes };\n  }\n}\n\nexport const osDetector = new OSDetector();\n"]}