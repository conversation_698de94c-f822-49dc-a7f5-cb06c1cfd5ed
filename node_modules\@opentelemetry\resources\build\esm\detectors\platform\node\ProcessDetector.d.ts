import { ResourceDetectionConfig } from '../../../config';
import { DetectedResource, ResourceDetector } from '../../../types';
/**
 * ProcessDetector will be used to detect the resources related current process running
 * and being instrumented from the NodeJS Process module.
 */
declare class ProcessDetector implements ResourceDetector {
    detect(_config?: ResourceDetectionConfig): DetectedResource;
}
export declare const processDetector: ProcessDetector;
export {};
//# sourceMappingURL=ProcessDetector.d.ts.map