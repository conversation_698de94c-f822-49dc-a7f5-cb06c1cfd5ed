import { ResourceDetectionConfig } from '../../../config';
import { DetectedResource, ResourceDetector } from '../../../types';
/**
 * ServiceInstanceIdDetector detects the resources related to the service instance ID.
 */
declare class ServiceInstanceIdDetector implements ResourceDetector {
    detect(_config?: ResourceDetectionConfig): DetectedResource;
}
/**
 * @experimental
 */
export declare const serviceInstanceIdDetector: ServiceInstanceIdDetector;
export {};
//# sourceMappingURL=ServiceInstanceIdDetector.d.ts.map