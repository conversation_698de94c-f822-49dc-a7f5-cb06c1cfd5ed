{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EACL,WAAW,EACX,YAAY,EACZ,UAAU,EACV,eAAe,EACf,yBAAyB,GAC1B,MAAM,aAAa,CAAC;AAErB,OAAO,EACL,sBAAsB,EACtB,eAAe,EACf,aAAa,GACd,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { ResourceDetectionConfig } from './config';\nexport { detectResources } from './detect-resources';\nexport {\n  envDetector,\n  hostDetector,\n  osDetector,\n  processDetector,\n  serviceInstanceIdDetector,\n} from './detectors';\nexport { Resource } from './Resource';\nexport {\n  resourceFromAttributes,\n  defaultResource,\n  emptyResource,\n} from './ResourceImpl';\nexport { defaultServiceName } from './platform';\nexport {\n  ResourceDetector,\n  DetectedResource,\n  DetectedResourceAttributes,\n  RawResourceAttribute,\n  MaybePromise,\n} from './types';\n"]}