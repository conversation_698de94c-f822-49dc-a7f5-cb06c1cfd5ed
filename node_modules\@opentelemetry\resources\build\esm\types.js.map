{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AttributeValue } from '@opentelemetry/api';\nimport { ResourceDetectionConfig } from './config';\n\n/**\n * Interface for a Resource Detector.\n * A resource detector returns a set of detected resource attributes.\n * A detected resource attribute may be an {@link AttributeValue} or a Promise of an AttributeValue.\n */\nexport interface ResourceDetector {\n  /**\n   * Detect resource attributes.\n   *\n   * @returns a {@link DetectedResource} object containing detected resource attributes\n   */\n  detect(config?: ResourceDetectionConfig): DetectedResource;\n}\n\nexport type DetectedResource = {\n  /**\n   * Detected resource attributes.\n   */\n  attributes?: DetectedResourceAttributes;\n};\n\n/**\n * An object representing detected resource attributes.\n * Value may be {@link AttributeValue}s, a promise to an {@link AttributeValue}, or undefined.\n */\ntype DetectedResourceAttributeValue = MaybePromise<AttributeValue | undefined>;\n\n/**\n * An object representing detected resource attributes.\n * Values may be {@link AttributeValue}s or a promise to an {@link AttributeValue}.\n */\nexport type DetectedResourceAttributes = Record<\n  string,\n  DetectedResourceAttributeValue\n>;\n\nexport type MaybePromise<T> = T | Promise<T>;\n\nexport type RawResourceAttribute = [\n  string,\n  MaybePromise<AttributeValue | undefined>,\n];\n"]}