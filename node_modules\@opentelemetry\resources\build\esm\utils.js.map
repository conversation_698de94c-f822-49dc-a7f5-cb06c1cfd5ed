{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAI,GAAY,EAAyB,EAAE;IACtE,OAAO,CACL,GAAG,KAAK,IAAI;QACZ,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAQ,GAA+B,CAAC,IAAI,KAAK,UAAU,CAC5D,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,UAAU,QAAQ,CAAI,CAAI;IAC9B,OAAO,CAAC,CAAC;AACX,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const isPromiseLike = <R>(val: unknown): val is PromiseLike<R> => {\n  return (\n    val !== null &&\n    typeof val === 'object' &&\n    typeof (val as Partial<PromiseLike<R>>).then === 'function'\n  );\n};\n\nexport function identity<T>(_: T): T {\n  return _;\n}\n"]}