import { ResourceDetectionConfig } from '../../../config';
import { DetectedResource, ResourceDetector } from '../../../types';
/**
 * HostDetector detects the resources related to the host current process is
 * running on. Currently only non-cloud-based attributes are included.
 */
declare class HostDetector implements ResourceDetector {
    detect(_config?: ResourceDetectionConfig): DetectedResource;
}
export declare const hostDetector: HostDetector;
export {};
//# sourceMappingURL=HostDetector.d.ts.map