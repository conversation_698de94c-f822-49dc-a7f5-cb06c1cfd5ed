{"version": 3, "file": "ServiceInstanceIdDetector.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/ServiceInstanceIdDetector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,8CAA4D;AAC5D,mCAAoC;AAIpC;;GAEG;AACH,MAAM,yBAAyB;IAC7B,MAAM,CAAC,OAAiC;QACtC,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,kCAAwB,CAAC,EAAE,IAAA,mBAAU,GAAE;aACzC;SACF,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACU,QAAA,yBAAyB,GAAG,IAAI,yBAAyB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ATTR_SERVICE_INSTANCE_ID } from '../../../semconv';\nimport { randomUUID } from 'crypto';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { DetectedResource, ResourceDetector } from '../../../types';\n\n/**\n * ServiceInstanceIdDetector detects the resources related to the service instance ID.\n */\nclass ServiceInstanceIdDetector implements ResourceDetector {\n  detect(_config?: ResourceDetectionConfig): DetectedResource {\n    return {\n      attributes: {\n        [ATTR_SERVICE_INSTANCE_ID]: randomUUID(),\n      },\n    };\n  }\n}\n\n/**\n * @experimental\n */\nexport const serviceInstanceIdDetector = new ServiceInstanceIdDetector();\n"]}