export { ResourceDetectionConfig } from './config';
export { detectResources } from './detect-resources';
export { envDetector, hostDetector, osDetector, processDetector, serviceInstanceIdDetector, } from './detectors';
export { Resource } from './Resource';
export { resourceFromAttributes, defaultResource, emptyResource, } from './ResourceImpl';
export { defaultServiceName } from './platform';
export { ResourceDetector, DetectedResource, DetectedResourceAttributes, RawResourceAttribute, MaybePromise, } from './types';
//# sourceMappingURL=index.d.ts.map