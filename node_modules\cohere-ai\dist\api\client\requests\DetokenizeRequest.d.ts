/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {
 *         tokens: [1],
 *         model: "model"
 *     }
 */
export interface DetokenizeRequest {
    /** The list of tokens to be detokenized. */
    tokens: number[];
    /** An optional parameter to provide the model name. This will ensure that the detokenization is done by the tokenizer used by that model. */
    model: string;
}
