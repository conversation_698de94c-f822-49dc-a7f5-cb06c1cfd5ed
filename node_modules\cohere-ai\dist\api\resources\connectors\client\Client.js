"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Connectors = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const Cohere = __importStar(require("../../../index"));
const url_join_1 = __importDefault(require("url-join"));
const serializers = __importStar(require("../../../../serialization/index"));
const errors = __importStar(require("../../../../errors/index"));
class Connectors {
    constructor(_options = {}) {
        this._options = _options;
    }
    /**
     * Returns a list of connectors ordered by descending creation date (newer first). See ['Managing your Connector'](https://docs.cohere.com/docs/managing-your-connector) for more information.
     *
     * @param {Cohere.ConnectorsListRequest} request
     * @param {Connectors.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.connectors.list()
     */
    list(request = {}, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { limit, offset } = request;
            const _queryParams = {};
            if (limit != null) {
                _queryParams["limit"] = limit.toString();
            }
            if (offset != null) {
                _queryParams["offset"] = offset.toString();
            }
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v1/connectors"),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.ListConnectorsResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(_response.error.body);
                    case 429:
                        throw new Cohere.TooManyRequestsError(_response.error.body);
                    case 498:
                        throw new Cohere.InvalidTokenError(_response.error.body);
                    case 499:
                        throw new Cohere.ClientClosedRequestError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling GET /v1/connectors.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Creates a new connector. The connector is tested during registration and will cancel registration when the test is unsuccessful. See ['Creating and Deploying a Connector'](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) for more information.
     *
     * @param {Cohere.CreateConnectorRequest} request
     * @param {Connectors.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.connectors.create({
     *         name: "name",
     *         url: "url"
     *     })
     */
    create(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v1/connectors"),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.CreateConnectorRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.CreateConnectorResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(_response.error.body);
                    case 429:
                        throw new Cohere.TooManyRequestsError(_response.error.body);
                    case 498:
                        throw new Cohere.InvalidTokenError(_response.error.body);
                    case 499:
                        throw new Cohere.ClientClosedRequestError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling POST /v1/connectors.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Retrieve a connector by ID. See ['Connectors'](https://docs.cohere.com/docs/connectors) for more information.
     *
     * @param {string} id - The ID of the connector to retrieve.
     * @param {Connectors.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.connectors.get("id")
     */
    get(id, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/connectors/${encodeURIComponent(id)}`),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.GetConnectorResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(_response.error.body);
                    case 429:
                        throw new Cohere.TooManyRequestsError(_response.error.body);
                    case 498:
                        throw new Cohere.InvalidTokenError(_response.error.body);
                    case 499:
                        throw new Cohere.ClientClosedRequestError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling GET /v1/connectors/{id}.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Delete a connector by ID. See ['Connectors'](https://docs.cohere.com/docs/connectors) for more information.
     *
     * @param {string} id - The ID of the connector to delete.
     * @param {Connectors.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.connectors.delete("id")
     */
    delete(id, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/connectors/${encodeURIComponent(id)}`),
                method: "DELETE",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.DeleteConnectorResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(_response.error.body);
                    case 429:
                        throw new Cohere.TooManyRequestsError(_response.error.body);
                    case 498:
                        throw new Cohere.InvalidTokenError(_response.error.body);
                    case 499:
                        throw new Cohere.ClientClosedRequestError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling DELETE /v1/connectors/{id}.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Update a connector by ID. Omitted fields will not be updated. See ['Managing your Connector'](https://docs.cohere.com/docs/managing-your-connector) for more information.
     *
     * @param {string} id - The ID of the connector to update.
     * @param {Cohere.UpdateConnectorRequest} request
     * @param {Connectors.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.connectors.update("id")
     */
    update(id, request = {}, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/connectors/${encodeURIComponent(id)}`),
                method: "PATCH",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.UpdateConnectorRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.UpdateConnectorResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(_response.error.body);
                    case 429:
                        throw new Cohere.TooManyRequestsError(_response.error.body);
                    case 498:
                        throw new Cohere.InvalidTokenError(_response.error.body);
                    case 499:
                        throw new Cohere.ClientClosedRequestError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling PATCH /v1/connectors/{id}.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * Authorize the connector with the given ID for the connector oauth app.  See ['Connector Authentication'](https://docs.cohere.com/docs/connector-authentication) for more information.
     *
     * @param {string} id - The ID of the connector to authorize.
     * @param {Cohere.ConnectorsOAuthAuthorizeRequest} request
     * @param {Connectors.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.connectors.oAuthAuthorize("id")
     */
    oAuthAuthorize(id, request = {}, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { afterTokenRedirect } = request;
            const _queryParams = {};
            if (afterTokenRedirect != null) {
                _queryParams["after_token_redirect"] = afterTokenRedirect;
            }
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/connectors/${encodeURIComponent(id)}/oauth/authorize`),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.OAuthAuthorizeResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 422:
                        throw new Cohere.UnprocessableEntityError(_response.error.body);
                    case 429:
                        throw new Cohere.TooManyRequestsError(_response.error.body);
                    case 498:
                        throw new Cohere.InvalidTokenError(_response.error.body);
                    case 499:
                        throw new Cohere.ClientClosedRequestError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 501:
                        throw new Cohere.NotImplementedError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    case 504:
                        throw new Cohere.GatewayTimeoutError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling POST /v1/connectors/{id}/oauth/authorize.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    _getAuthorizationHeader() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const bearer = (_a = (yield core.Supplier.get(this._options.token))) !== null && _a !== void 0 ? _a : process === null || process === void 0 ? void 0 : process.env["CO_API_KEY"];
            if (bearer == null) {
                throw new errors.CohereError({
                    message: "Please specify CO_API_KEY when instantiating the client.",
                });
            }
            return `Bearer ${bearer}`;
        });
    }
}
exports.Connectors = Connectors;
