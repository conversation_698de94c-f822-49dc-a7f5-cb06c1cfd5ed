/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../../../../index";
/**
 * @example
 *     {}
 */
export interface DatasetsListRequest {
    /**
     * optional filter by dataset type
     */
    datasetType?: string;
    /**
     * optional filter before a date
     */
    before?: Date;
    /**
     * optional filter after a date
     */
    after?: Date;
    /**
     * optional limit to number of results
     */
    limit?: number;
    /**
     * optional offset to start of results
     */
    offset?: number;
    /**
     * optional filter by validation status
     */
    validationStatus?: Cohere.DatasetValidationStatus;
}
