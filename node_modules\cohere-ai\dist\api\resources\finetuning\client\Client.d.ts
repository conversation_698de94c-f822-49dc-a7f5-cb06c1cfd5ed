/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as Cohere from "../../../index";
export declare namespace Finetuning {
    interface Options {
        environment?: core.Supplier<environments.CohereEnvironment | string>;
        token?: core.Supplier<core.BearerToken | undefined>;
        /** Override the X-Client-Name header */
        clientName?: core.Supplier<string | undefined>;
        fetcher?: core.FetchFunction;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the X-Client-Name header */
        clientName?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string>;
    }
}
/**
 * Finetuning API (Beta)
 */
export declare class Finetuning {
    protected readonly _options: Finetuning.Options;
    constructor(_options?: Finetuning.Options);
    /**
     * @param {Cohere.FinetuningListFinetunedModelsRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.listFinetunedModels()
     */
    listFinetunedModels(request?: Cohere.FinetuningListFinetunedModelsRequest, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.ListFinetunedModelsResponse>;
    /**
     * @param {Cohere.finetuning.FinetunedModel} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.createFinetunedModel({
     *         name: "api-test",
     *         settings: {
     *             baseModel: {
     *                 baseType: "BASE_TYPE_CHAT"
     *             },
     *             datasetId: "my-dataset-id"
     *         }
     *     })
     */
    createFinetunedModel(request: Cohere.finetuning.FinetunedModel, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.CreateFinetunedModelResponse>;
    /**
     * @param {string} id - The fine-tuned model ID.
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.getFinetunedModel("id")
     */
    getFinetunedModel(id: string, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.GetFinetunedModelResponse>;
    /**
     * @param {string} id - The fine-tuned model ID.
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.deleteFinetunedModel("id")
     */
    deleteFinetunedModel(id: string, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.DeleteFinetunedModelResponse>;
    /**
     * @param {string} id - FinetunedModel ID.
     * @param {Cohere.FinetuningUpdateFinetunedModelRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.updateFinetunedModel("id", {
     *         name: "name",
     *         settings: {
     *             baseModel: {
     *                 baseType: "BASE_TYPE_UNSPECIFIED"
     *             },
     *             datasetId: "dataset_id"
     *         }
     *     })
     */
    updateFinetunedModel(id: string, request: Cohere.FinetuningUpdateFinetunedModelRequest, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.UpdateFinetunedModelResponse>;
    /**
     * @param {string} finetunedModelId - The parent fine-tuned model ID.
     * @param {Cohere.FinetuningListEventsRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.listEvents("finetuned_model_id")
     */
    listEvents(finetunedModelId: string, request?: Cohere.FinetuningListEventsRequest, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.ListEventsResponse>;
    /**
     * @param {string} finetunedModelId - The parent fine-tuned model ID.
     * @param {Cohere.FinetuningListTrainingStepMetricsRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.listTrainingStepMetrics("finetuned_model_id")
     */
    listTrainingStepMetrics(finetunedModelId: string, request?: Cohere.FinetuningListTrainingStepMetricsRequest, requestOptions?: Finetuning.RequestOptions): Promise<Cohere.finetuning.ListTrainingStepMetricsResponse>;
    protected _getAuthorizationHeader(): Promise<string>;
}
