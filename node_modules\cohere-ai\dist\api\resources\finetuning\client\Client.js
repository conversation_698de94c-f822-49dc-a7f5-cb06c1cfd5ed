"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Finetuning = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const Cohere = __importStar(require("../../../index"));
const url_join_1 = __importDefault(require("url-join"));
const serializers = __importStar(require("../../../../serialization/index"));
const errors = __importStar(require("../../../../errors/index"));
/**
 * Finetuning API (Beta)
 */
class Finetuning {
    constructor(_options = {}) {
        this._options = _options;
    }
    /**
     * @param {Cohere.FinetuningListFinetunedModelsRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.listFinetunedModels()
     */
    listFinetunedModels(request = {}, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { pageSize, pageToken, orderBy } = request;
            const _queryParams = {};
            if (pageSize != null) {
                _queryParams["page_size"] = pageSize.toString();
            }
            if (pageToken != null) {
                _queryParams["page_token"] = pageToken;
            }
            if (orderBy != null) {
                _queryParams["order_by"] = orderBy;
            }
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v1/finetuning/finetuned-models"),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.ListFinetunedModelsResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling GET /v1/finetuning/finetuned-models.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * @param {Cohere.finetuning.FinetunedModel} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.createFinetunedModel({
     *         name: "api-test",
     *         settings: {
     *             baseModel: {
     *                 baseType: "BASE_TYPE_CHAT"
     *             },
     *             datasetId: "my-dataset-id"
     *         }
     *     })
     */
    createFinetunedModel(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, "v1/finetuning/finetuned-models"),
                method: "POST",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.finetuning.FinetunedModel.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.CreateFinetunedModelResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling POST /v1/finetuning/finetuned-models.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * @param {string} id - The fine-tuned model ID.
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.getFinetunedModel("id")
     */
    getFinetunedModel(id, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/finetuning/finetuned-models/${encodeURIComponent(id)}`),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.GetFinetunedModelResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling GET /v1/finetuning/finetuned-models/{id}.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * @param {string} id - The fine-tuned model ID.
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.deleteFinetunedModel("id")
     */
    deleteFinetunedModel(id, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/finetuning/finetuned-models/${encodeURIComponent(id)}`),
                method: "DELETE",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.DeleteFinetunedModelResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling DELETE /v1/finetuning/finetuned-models/{id}.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * @param {string} id - FinetunedModel ID.
     * @param {Cohere.FinetuningUpdateFinetunedModelRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.updateFinetunedModel("id", {
     *         name: "name",
     *         settings: {
     *             baseModel: {
     *                 baseType: "BASE_TYPE_UNSPECIFIED"
     *             },
     *             datasetId: "dataset_id"
     *         }
     *     })
     */
    updateFinetunedModel(id, request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/finetuning/finetuned-models/${encodeURIComponent(id)}`),
                method: "PATCH",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.FinetuningUpdateFinetunedModelRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.UpdateFinetunedModelResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling PATCH /v1/finetuning/finetuned-models/{id}.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * @param {string} finetunedModelId - The parent fine-tuned model ID.
     * @param {Cohere.FinetuningListEventsRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.listEvents("finetuned_model_id")
     */
    listEvents(finetunedModelId, request = {}, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { pageSize, pageToken, orderBy } = request;
            const _queryParams = {};
            if (pageSize != null) {
                _queryParams["page_size"] = pageSize.toString();
            }
            if (pageToken != null) {
                _queryParams["page_token"] = pageToken;
            }
            if (orderBy != null) {
                _queryParams["order_by"] = orderBy;
            }
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/finetuning/finetuned-models/${encodeURIComponent(finetunedModelId)}/events`),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.ListEventsResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling GET /v1/finetuning/finetuned-models/{finetuned_model_id}/events.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    /**
     * @param {string} finetunedModelId - The parent fine-tuned model ID.
     * @param {Cohere.FinetuningListTrainingStepMetricsRequest} request
     * @param {Finetuning.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.ServiceUnavailableError}
     *
     * @example
     *     await client.finetuning.listTrainingStepMetrics("finetuned_model_id")
     */
    listTrainingStepMetrics(finetunedModelId, request = {}, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { pageSize, pageToken } = request;
            const _queryParams = {};
            if (pageSize != null) {
                _queryParams["page_size"] = pageSize.toString();
            }
            if (pageToken != null) {
                _queryParams["page_token"] = pageToken;
            }
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_b = (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.CohereEnvironment.Production, `v1/finetuning/finetuned-models/${encodeURIComponent(finetunedModelId)}/training-step-metrics`),
                method: "GET",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Client-Name": (yield core.Supplier.get(this._options.clientName)) != null
                        ? yield core.Supplier.get(this._options.clientName)
                        : undefined, "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "cohere-ai", "X-Fern-SDK-Version": "7.17.1", "User-Agent": "cohere-ai/7.17.1", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 300000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return serializers.finetuning.ListTrainingStepMetricsResponse.parseOrThrow(_response.body, {
                    unrecognizedObjectKeys: "passthrough",
                    allowUnrecognizedUnionMembers: true,
                    allowUnrecognizedEnumValues: true,
                    skipValidation: true,
                    breadcrumbsPrefix: ["response"],
                });
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new Cohere.BadRequestError(_response.error.body);
                    case 401:
                        throw new Cohere.UnauthorizedError(_response.error.body);
                    case 403:
                        throw new Cohere.ForbiddenError(_response.error.body);
                    case 404:
                        throw new Cohere.NotFoundError(_response.error.body);
                    case 500:
                        throw new Cohere.InternalServerError(_response.error.body);
                    case 503:
                        throw new Cohere.ServiceUnavailableError(_response.error.body);
                    default:
                        throw new errors.CohereError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.CohereError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.CohereTimeoutError("Timeout exceeded when calling GET /v1/finetuning/finetuned-models/{finetuned_model_id}/training-step-metrics.");
                case "unknown":
                    throw new errors.CohereError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    _getAuthorizationHeader() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const bearer = (_a = (yield core.Supplier.get(this._options.token))) !== null && _a !== void 0 ? _a : process === null || process === void 0 ? void 0 : process.env["CO_API_KEY"];
            if (bearer == null) {
                throw new errors.CohereError({
                    message: "Please specify CO_API_KEY when instantiating the client.",
                });
            }
            return `Bearer ${bearer}`;
        });
    }
}
exports.Finetuning = Finetuning;
