/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../../../../../index";
/**
 * The base model used for fine-tuning.
 */
export interface BaseModel {
    /** The name of the base model. */
    name?: string;
    /** read-only. The version of the base model. */
    version?: string;
    /** The type of the base model. */
    baseType: Cohere.finetuning.BaseType;
    /** Deprecated: The fine-tuning strategy. */
    strategy?: Cohere.finetuning.Strategy;
}
