/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The possible types of fine-tuned models.
 *
 *  - BASE_TYPE_UNSPECIFIED: Unspecified model.
 *  - BASE_TYPE_GENERATIVE: Deprecated: Generative model.
 *  - BASE_TYPE_CLASSIFICATION: Classification model.
 *  - BASE_TYPE_RERANK: Rerank model.
 *  - BASE_TYPE_CHAT: Chat model.
 */
export declare type BaseType = "BASE_TYPE_UNSPECIFIED" | "BASE_TYPE_GENERATIVE" | "BASE_TYPE_CLASSIFICATION" | "BASE_TYPE_RERANK" | "BASE_TYPE_CHAT";
export declare const BaseType: {
    readonly BaseTypeUnspecified: "BASE_TYPE_UNSPECIFIED";
    readonly BaseTypeGenerative: "BASE_TYPE_GENERATIVE";
    readonly BaseTypeClassification: "BASE_TYPE_CLASSIFICATION";
    readonly BaseTypeRerank: "BASE_TYPE_RERANK";
    readonly BaseTypeChat: "BASE_TYPE_CHAT";
};
