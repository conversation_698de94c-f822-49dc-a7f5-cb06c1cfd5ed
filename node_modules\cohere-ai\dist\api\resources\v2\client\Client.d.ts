/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as Cohere from "../../../index";
export declare namespace V2 {
    interface Options {
        environment?: core.Supplier<environments.CohereEnvironment | string>;
        token?: core.Supplier<core.BearerToken | undefined>;
        /** Override the X-Client-Name header */
        clientName?: core.Supplier<string | undefined>;
        fetcher?: core.FetchFunction;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the X-Client-Name header */
        clientName?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string>;
    }
}
export declare class V2 {
    protected readonly _options: V2.Options;
    constructor(_options?: V2.Options);
    /**
     * Generates a text response to a user message. To learn how to use the Chat API and RAG follow our [Text Generation guides](https://docs.cohere.com/v2/docs/chat-api).
     *
     * Follow the [Migration Guide](https://docs.cohere.com/v2/docs/migrating-v1-to-v2) for instructions on moving from API v1 to API v2.
     */
    chatStream(request: Cohere.V2ChatStreamRequest, requestOptions?: V2.RequestOptions): Promise<core.Stream<Cohere.StreamedChatResponseV2>>;
    /**
     * Generates a text response to a user message and streams it down, token by token. To learn how to use the Chat API with streaming follow our [Text Generation guides](https://docs.cohere.com/v2/docs/chat-api).
     *
     * Follow the [Migration Guide](https://docs.cohere.com/v2/docs/migrating-v1-to-v2) for instructions on moving from API v1 to API v2.
     *
     * @param {Cohere.V2ChatRequest} request
     * @param {V2.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.v2.chat({
     *         model: "model",
     *         messages: [{
     *                 role: "tool",
     *                 toolCallId: "messages",
     *                 content: "messages"
     *             }]
     *     })
     */
    chat(request: Cohere.V2ChatRequest, requestOptions?: V2.RequestOptions): Promise<Cohere.ChatResponse>;
    /**
     * This endpoint returns text embeddings. An embedding is a list of floating point numbers that captures semantic information about the text that it represents.
     *
     * Embeddings can be used to create text classifiers as well as empower semantic search. To learn more about embeddings, see the embedding page.
     *
     * If you want to learn more how to use the embedding model, have a look at the [Semantic Search Guide](https://docs.cohere.com/docs/semantic-search).
     *
     * @param {Cohere.V2EmbedRequest} request
     * @param {V2.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.v2.embed({
     *         model: "model",
     *         inputType: "search_document",
     *         embeddingTypes: ["float"]
     *     })
     */
    embed(request: Cohere.V2EmbedRequest, requestOptions?: V2.RequestOptions): Promise<Cohere.EmbedByTypeResponse>;
    /**
     * This endpoint takes in a query and a list of texts and produces an ordered array with each text assigned a relevance score.
     *
     * @param {Cohere.V2RerankRequest} request
     * @param {V2.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link Cohere.BadRequestError}
     * @throws {@link Cohere.UnauthorizedError}
     * @throws {@link Cohere.ForbiddenError}
     * @throws {@link Cohere.NotFoundError}
     * @throws {@link Cohere.UnprocessableEntityError}
     * @throws {@link Cohere.TooManyRequestsError}
     * @throws {@link Cohere.InvalidTokenError}
     * @throws {@link Cohere.ClientClosedRequestError}
     * @throws {@link Cohere.InternalServerError}
     * @throws {@link Cohere.NotImplementedError}
     * @throws {@link Cohere.ServiceUnavailableError}
     * @throws {@link Cohere.GatewayTimeoutError}
     *
     * @example
     *     await client.v2.rerank({
     *         model: "model",
     *         query: "query",
     *         documents: ["documents"]
     *     })
     */
    rerank(request: Cohere.V2RerankRequest, requestOptions?: V2.RequestOptions): Promise<Cohere.V2RerankResponse>;
    protected _getAuthorizationHeader(): Promise<string>;
}
