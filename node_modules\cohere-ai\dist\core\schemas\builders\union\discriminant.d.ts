export declare function discriminant<RawDiscriminant extends string, ParsedDiscriminant extends string>(parsedDiscriminant: ParsedDiscriminant, rawDiscriminant: RawDiscriminant): Discriminant<RawDiscriminant, ParsedDiscriminant>;
export interface Discriminant<RawDiscriminant extends string, ParsedDiscriminant extends string> {
    parsedDiscriminant: ParsedDiscriminant;
    rawDiscriminant: RawDiscriminant;
}
