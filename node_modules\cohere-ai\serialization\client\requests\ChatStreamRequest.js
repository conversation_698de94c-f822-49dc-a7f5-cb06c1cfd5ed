"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatStreamRequest = void 0;
const core = __importStar(require("../../../core"));
const Message_1 = require("../../types/Message");
const ChatStreamRequestPromptTruncation_1 = require("../../types/ChatStreamRequestPromptTruncation");
const ChatConnector_1 = require("../../types/ChatConnector");
const ChatDocument_1 = require("../../types/ChatDocument");
const ChatStreamRequestCitationQuality_1 = require("../../types/ChatStreamRequestCitationQuality");
const Tool_1 = require("../../types/Tool");
const ToolResult_1 = require("../../types/ToolResult");
const ResponseFormat_1 = require("../../types/ResponseFormat");
const ChatStreamRequestSafetyMode_1 = require("../../types/ChatStreamRequestSafetyMode");
exports.ChatStreamRequest = core.serialization.object({
    message: core.serialization.string(),
    model: core.serialization.string().optional(),
    preamble: core.serialization.string().optional(),
    chatHistory: core.serialization.property("chat_history", core.serialization.list(Message_1.Message).optional()),
    conversationId: core.serialization.property("conversation_id", core.serialization.string().optional()),
    promptTruncation: core.serialization.property("prompt_truncation", ChatStreamRequestPromptTruncation_1.ChatStreamRequestPromptTruncation.optional()),
    connectors: core.serialization.list(ChatConnector_1.ChatConnector).optional(),
    searchQueriesOnly: core.serialization.property("search_queries_only", core.serialization.boolean().optional()),
    documents: core.serialization.list(ChatDocument_1.ChatDocument).optional(),
    citationQuality: core.serialization.property("citation_quality", ChatStreamRequestCitationQuality_1.ChatStreamRequestCitationQuality.optional()),
    temperature: core.serialization.number().optional(),
    maxTokens: core.serialization.property("max_tokens", core.serialization.number().optional()),
    maxInputTokens: core.serialization.property("max_input_tokens", core.serialization.number().optional()),
    k: core.serialization.number().optional(),
    p: core.serialization.number().optional(),
    seed: core.serialization.number().optional(),
    stopSequences: core.serialization.property("stop_sequences", core.serialization.list(core.serialization.string()).optional()),
    frequencyPenalty: core.serialization.property("frequency_penalty", core.serialization.number().optional()),
    presencePenalty: core.serialization.property("presence_penalty", core.serialization.number().optional()),
    rawPrompting: core.serialization.property("raw_prompting", core.serialization.boolean().optional()),
    returnPrompt: core.serialization.property("return_prompt", core.serialization.boolean().optional()),
    tools: core.serialization.list(Tool_1.Tool).optional(),
    toolResults: core.serialization.property("tool_results", core.serialization.list(ToolResult_1.ToolResult).optional()),
    forceSingleStep: core.serialization.property("force_single_step", core.serialization.boolean().optional()),
    responseFormat: core.serialization.property("response_format", ResponseFormat_1.ResponseFormat.optional()),
    safetyMode: core.serialization.property("safety_mode", ChatStreamRequestSafetyMode_1.ChatStreamRequestSafetyMode.optional()),
});
