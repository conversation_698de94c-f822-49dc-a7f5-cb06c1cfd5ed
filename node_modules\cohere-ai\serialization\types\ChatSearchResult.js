"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatSearchResult = void 0;
const core = __importStar(require("../../core"));
const ChatSearchQuery_1 = require("./ChatSearchQuery");
const ChatSearchResultConnector_1 = require("./ChatSearchResultConnector");
exports.ChatSearchResult = core.serialization.object({
    searchQuery: core.serialization.property("search_query", ChatSearchQuery_1.ChatSearchQuery.optional()),
    connector: ChatSearchResultConnector_1.ChatSearchResultConnector,
    documentIds: core.serialization.property("document_ids", core.serialization.list(core.serialization.string())),
    errorMessage: core.serialization.property("error_message", core.serialization.string().optional()),
    continueOnFailure: core.serialization.property("continue_on_failure", core.serialization.boolean().optional()),
});
